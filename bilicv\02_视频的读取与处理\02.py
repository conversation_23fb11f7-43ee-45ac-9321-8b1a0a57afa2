# 250217

import cv2
import time

pre_time = time.perf_counter_ns()

vc = cv2.VideoCapture(1)

# 检查打开是否正确
if vc.isOpened():
    open, frame = vc.read()
else:
    open = False
    print('False')


while open:
    ret, frame = vc.read()
    real_time = time.perf_counter_ns()
    if frame is None:
        break
    if ret is True:
        fps = 1000000000 / (real_time - pre_time)
        cur_img = frame.copy()
        cur_img[:, :, 1] = 0
        cur_img[:, :, 0] = 0
        ret, thresh04 = cv2.threshold(cur_img, 180, 255, cv2.THRESH_TOZERO)
        cv2.imshow('blue', thresh04)
        cv2.putText(frame, str(fps), (0, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.65, (0, 255, 0), 1)
        cv2.imshow('result', frame)
        pre_time = time.perf_counter_ns()
        if cv2.waitKey(1) & 0xFF == 27:
            break
vc.release()
cv2.destroyAllWindows()
