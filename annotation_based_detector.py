#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于标注数据的激光点检测器
根据用户标注数据优化检测算法，减少误识别
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime

class AnnotationBasedDetector:
    def __init__(self, annotation_file="laser_annotations/quick_annotations_20250723_192904.json"):
        self.annotation_file = annotation_file
        self.params = self._analyze_annotations_and_optimize()
        print("基于标注数据优化完成")
        
    def _analyze_annotations_and_optimize(self):
        """分析标注数据并优化参数"""
        print("正在分析标注数据...")
        
        # 加载标注数据
        with open(self.annotation_file, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        print(f"加载了 {len(annotations)} 个标注点")
        
        # 去除重复标注
        unique_annotations = []
        seen_frames = set()
        for ann in annotations:
            if ann['frame'] not in seen_frames:
                unique_annotations.append(ann)
                seen_frames.add(ann['frame'])
        
        print(f"去重后有 {len(unique_annotations)} 个唯一标注点")
        
        # 分析激光点特征
        features = self._extract_features_from_annotations(unique_annotations)
        
        # 基于特征优化参数
        optimized_params = self._optimize_parameters(features)
        
        return optimized_params
    
    def _extract_features_from_annotations(self, annotations):
        """从标注数据提取激光点特征"""
        cap = cv2.VideoCapture('output.mp4')
        features = []
        
        print("正在提取激光点特征...")
        
        for i, ann in enumerate(annotations[:50]):  # 只分析前50个点以提高速度
            frame_num = ann['frame']
            roi_x, roi_y = ann.get('roi_x'), ann.get('roi_y')
            
            if roi_x is None or roi_y is None:
                continue
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
            ret, frame = cap.read()
            
            if ret:
                frame = cv2.resize(frame, (640, 480))
                roi_frame = frame[65:410, 260:470, :]
                
                feature = self._extract_point_features(roi_frame, (roi_x, roi_y))
                if feature:
                    features.append(feature)
                    
            if i % 10 == 0:
                print(f"已处理 {i+1}/{min(50, len(annotations))} 个标注点")
        
        cap.release()
        print(f"成功提取了 {len(features)} 个特征")
        return features
    
    def _extract_point_features(self, image, point):
        """提取单个点的特征"""
        x, y = point
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape
        
        if x < 10 or y < 10 or x >= w-10 or y >= h-10:
            return None
        
        # 亮度特征
        center_val = gray[y, x]
        local_region = gray[y-5:y+6, x-5:x+6]
        avg_surrounding = np.mean(local_region)
        contrast = center_val - avg_surrounding
        
        # 颜色特征
        b, g, r = image[y, x]
        
        # 周围亮度分布
        circle_vals = []
        for radius in [3, 5]:
            angles = np.linspace(0, 2*np.pi, 8, endpoint=False)
            for angle in angles:
                px = int(x + radius * np.cos(angle))
                py = int(y + radius * np.sin(angle))
                if 0 <= px < w and 0 <= py < h:
                    circle_vals.append(gray[py, px])
        
        return {
            'center_brightness': center_val,
            'avg_surrounding': avg_surrounding,
            'contrast': contrast,
            'red_component': r,
            'green_component': g,
            'blue_component': b,
            'red_dominance': r - max(g, b),
            'circle_brightness_std': np.std(circle_vals) if circle_vals else 0,
            'max_local_brightness': np.max(local_region),
            'min_local_brightness': np.min(local_region)
        }
    
    def _optimize_parameters(self, features):
        """基于特征优化检测参数"""
        if not features:
            return self._get_default_params()
        
        # 统计特征
        brightness_values = [f['center_brightness'] for f in features]
        contrast_values = [f['contrast'] for f in features]
        red_values = [f['red_component'] for f in features]
        red_dominance_values = [f['red_dominance'] for f in features]
        
        # 计算统计量
        min_brightness = np.percentile(brightness_values, 10)
        avg_brightness = np.mean(brightness_values)
        min_contrast = np.percentile(contrast_values, 10)
        avg_contrast = np.mean(contrast_values)
        min_red = np.percentile(red_values, 10)
        min_red_dominance = np.percentile(red_dominance_values, 10)
        
        print(f"特征统计:")
        print(f"  亮度范围: {min_brightness:.1f} - {np.max(brightness_values):.1f}, 平均: {avg_brightness:.1f}")
        print(f"  对比度范围: {min_contrast:.1f} - {np.max(contrast_values):.1f}, 平均: {avg_contrast:.1f}")
        print(f"  红色分量范围: {min_red:.1f} - {np.max(red_values):.1f}")
        print(f"  红色优势范围: {min_red_dominance:.1f} - {np.max(red_dominance_values):.1f}")
        
        # 优化参数
        params = {
            'min_brightness': max(180, min_brightness - 20),
            'brightness_threshold_offset': max(10, avg_contrast - 10),
            'min_contrast': max(8, min_contrast - 5),
            'min_red_component': max(100, min_red - 20),
            'min_red_dominance': max(10, min_red_dominance - 10),
            'min_area': 1,
            'max_area': 150,
            'min_circularity': 0.2,
            'score_threshold': 0.3
        }
        
        print(f"优化后参数:")
        for key, value in params.items():
            print(f"  {key}: {value}")
        
        return params
    
    def _get_default_params(self):
        """获取默认参数"""
        return {
            'min_brightness': 200,
            'brightness_threshold_offset': 15,
            'min_contrast': 15,
            'min_red_component': 120,
            'min_red_dominance': 20,
            'min_area': 2,
            'max_area': 100,
            'min_circularity': 0.3,
            'score_threshold': 0.4
        }
    
    def detect_laser_point(self, image):
        """检测激光点"""
        if image is None:
            return None
        
        candidates = []
        
        # 主要检测方法：优化的亮度检测
        bright_candidates = self._optimized_brightness_detection(image)
        candidates.extend(bright_candidates)
        
        # 辅助检测方法：优化的红色检测
        if len(candidates) < 2:
            red_candidates = self._optimized_red_detection(image)
            candidates.extend(red_candidates)
        
        # 选择最佳候选点
        return self._select_best_candidate(candidates)
    
    def _optimized_brightness_detection(self, image):
        """优化的亮度检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        max_val = np.max(gray)
        mean_val = np.mean(gray)
        
        # 使用优化的阈值
        if max_val < self.params['min_brightness']:
            return candidates
        
        threshold = max_val - self.params['brightness_threshold_offset']
        threshold = max(threshold, self.params['min_brightness'])
        
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        
        # 形态学操作
        kernel = np.ones((3, 3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.params['min_area'] <= area <= self.params['max_area']:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    if self._verify_laser_features(gray, image, (cx, cy)):
                        # 计算得分
                        brightness_score = gray[cy, cx] / 255.0
                        area_score = min(area / 50.0, 1.0)
                        
                        # 圆形度
                        perimeter = cv2.arcLength(contour, True)
                        if perimeter > 0:
                            circularity = 4 * np.pi * area / (perimeter ** 2)
                        else:
                            circularity = 0
                        
                        if circularity >= self.params['min_circularity']:
                            score = brightness_score * 0.5 + area_score * 0.2 + circularity * 0.3
                            
                            if score >= self.params['score_threshold']:
                                candidates.append({
                                    'point': (cx, cy),
                                    'score': score,
                                    'method': 'brightness'
                                })
        
        return candidates
    
    def _optimized_red_detection(self, image):
        """优化的红色检测"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 红色范围
        lower_red1 = np.array([0, 80, 120])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 80, 120])
        upper_red2 = np.array([180, 255, 255])
        
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(mask1, mask2)
        
        # 亮度掩码
        _, brightness_mask = cv2.threshold(gray, self.params['min_brightness'], 255, cv2.THRESH_BINARY)
        
        # 融合掩码
        final_mask = cv2.bitwise_and(red_mask, brightness_mask)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.params['min_area'] <= area <= self.params['max_area']:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    if self._verify_red_features(image, (cx, cy)):
                        score = min(area / 60.0, 1.0)
                        candidates.append({
                            'point': (cx, cy),
                            'score': score,
                            'method': 'red'
                        })
        
        return candidates
    
    def _verify_laser_features(self, gray, color_image, point):
        """验证激光点特征"""
        x, y = point
        h, w = gray.shape
        
        if x < 5 or y < 5 or x >= w-5 or y >= h-5:
            return False
        
        center_val = gray[y, x]
        if center_val < self.params['min_brightness']:
            return False
        
        # 对比度检查
        local_region = gray[y-3:y+4, x-3:x+4]
        avg_surrounding = np.mean(local_region)
        contrast = center_val - avg_surrounding
        
        if contrast < self.params['min_contrast']:
            return False
        
        return True
    
    def _verify_red_features(self, image, point):
        """验证红色特征"""
        x, y = point
        h, w = image.shape[:2]
        
        if x < 2 or y < 2 or x >= w-2 or y >= h-2:
            return False
        
        b, g, r = image[y, x]
        
        if r < self.params['min_red_component']:
            return False
        
        if r < g + self.params['min_red_dominance'] or r < b + self.params['min_red_dominance']:
            return False
        
        return True
    
    def _select_best_candidate(self, candidates):
        """选择最佳候选点"""
        if not candidates:
            return None
        
        if len(candidates) == 1:
            return candidates[0]['point']
        
        # 按得分排序
        candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)
        
        # 简单聚类选择
        best_candidate = candidates[0]
        
        # 检查是否有相近的高分候选点
        for other in candidates[1:]:
            distance = np.sqrt((best_candidate['point'][0] - other['point'][0])**2 + 
                             (best_candidate['point'][1] - other['point'][1])**2)
            if distance <= 8:
                # 如果有相近的候选点，增加置信度
                best_candidate['score'] += other['score'] * 0.2
                break
        
        return best_candidate['point']

def test_annotation_based_detector():
    """测试基于标注的检测器"""
    detector = AnnotationBasedDetector()
    
    cap = cv2.VideoCapture('output.mp4')
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("\n开始测试基于标注数据的检测器...")
    
    import time
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        frame = cv2.resize(frame, (640, 480))
        
        # 保存原始帧用于显示
        display_frame = frame.copy()
        
        # 提取ROI区域
        roi_frame = frame[65:410, 260:470, :]
        roi_offset = (260, 65)
        
        start_time = time.time()
        
        # 检测激光点
        laser_point = detector.detect_laser_point(roi_frame)
        
        end_time = time.time()
        total_time += (end_time - start_time)
        
        if laser_point:
            detection_count += 1
            # 转换回原图坐标
            x = laser_point[0] + roi_offset[0]
            y = laser_point[1] + roi_offset[1]
            
            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y}) [ROI: ({laser_point[0]}, {laser_point[1]})]")
            
            # 在原图上绘制检测结果
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(display_frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 绘制ROI区域边界
            cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
            cv2.putText(display_frame, "ROI", (265, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 显示FPS和状态信息
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(display_frame, f"FPS: {current_fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # 显示检测信息
            detection_rate = detection_count / frame_count * 100
            cv2.putText(display_frame, f"Detection: {detection_rate:.1f}%", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 每5帧显示一次
        if frame_count % 5 == 0:
            cv2.imshow('Annotation-Based Laser Detection', display_frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 基于标注数据的检测结果 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")

def compare_with_annotations():
    """与标注数据进行比较分析"""
    detector = AnnotationBasedDetector()

    # 加载标注数据
    with open(detector.annotation_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)

    # 去除重复标注
    unique_annotations = []
    seen_frames = set()
    for ann in annotations:
        if ann['frame'] not in seen_frames:
            unique_annotations.append(ann)
            seen_frames.add(ann['frame'])

    print(f"开始比较分析，共 {len(unique_annotations)} 个标注帧")

    cap = cv2.VideoCapture('output.mp4')
    comparison_results = []

    for i, ann in enumerate(unique_annotations):
        frame_num = ann['frame']
        true_roi_x, true_roi_y = ann.get('roi_x'), ann.get('roi_y')

        if true_roi_x is None or true_roi_y is None:
            continue

        # 加载对应帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()

        if ret:
            frame = cv2.resize(frame, (640, 480))
            roi_frame = frame[65:410, 260:470, :]

            # 检测激光点
            detected = detector.detect_laser_point(roi_frame)

            if detected:
                # 计算误差
                error_x = abs(detected[0] - true_roi_x)
                error_y = abs(detected[1] - true_roi_y)
                distance_error = np.sqrt(error_x**2 + error_y**2)

                result = {
                    'frame': frame_num,
                    'true_position': (true_roi_x, true_roi_y),
                    'detected_position': detected,
                    'error_x': error_x,
                    'error_y': error_y,
                    'distance_error': distance_error,
                    'detection_success': True
                }
            else:
                result = {
                    'frame': frame_num,
                    'true_position': (true_roi_x, true_roi_y),
                    'detected_position': None,
                    'detection_success': False
                }

            comparison_results.append(result)

            if i % 20 == 0:
                print(f"已处理 {i+1}/{len(unique_annotations)} 帧")

    cap.release()

    # 分析结果
    analyze_comparison_results(comparison_results)

    # 保存比较结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"laser_annotations/annotation_comparison_{timestamp}.json"

    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_results, f, indent=2)

    print(f"比较结果已保存到: {result_file}")

    return comparison_results

def analyze_comparison_results(results):
    """分析比较结果"""
    print("\n" + "=" * 60)
    print("基于标注数据的检测算法性能分析")
    print("=" * 60)

    successful_detections = [r for r in results if r.get('detection_success', False) and 'distance_error' in r]
    failed_detections = [r for r in results if not r.get('detection_success', False)]

    total_annotations = len(results)
    success_count = len(successful_detections)
    fail_count = len(failed_detections)

    print(f"总标注帧数: {total_annotations}")
    print(f"成功检测帧数: {success_count}")
    print(f"检测失败帧数: {fail_count}")
    print(f"检测成功率: {success_count/total_annotations*100:.1f}%")

    if successful_detections:
        errors = [r['distance_error'] for r in successful_detections]

        print(f"\n误差统计:")
        print(f"  平均误差: {np.mean(errors):.2f} 像素")
        print(f"  中位数误差: {np.median(errors):.2f} 像素")
        print(f"  最大误差: {np.max(errors):.2f} 像素")
        print(f"  最小误差: {np.min(errors):.2f} 像素")
        print(f"  误差标准差: {np.std(errors):.2f} 像素")

        # 误差分布
        excellent = len([e for e in errors if e <= 3])
        good = len([e for e in errors if 3 < e <= 8])
        acceptable = len([e for e in errors if 8 < e <= 15])
        poor = len([e for e in errors if e > 15])

        print(f"\n误差分布:")
        print(f"  优秀 (≤3像素): {excellent} ({excellent/len(errors)*100:.1f}%)")
        print(f"  良好 (3-8像素): {good} ({good/len(errors)*100:.1f}%)")
        print(f"  可接受 (8-15像素): {acceptable} ({acceptable/len(errors)*100:.1f}%)")
        print(f"  较差 (>15像素): {poor} ({poor/len(errors)*100:.1f}%)")

        # 找出误差最大的帧
        if errors:
            max_error_idx = np.argmax(errors)
            max_error_result = successful_detections[max_error_idx]
            print(f"\n最大误差帧:")
            print(f"  帧号: {max_error_result['frame']}")
            print(f"  真实位置: {max_error_result['true_position']}")
            print(f"  检测位置: {max_error_result['detected_position']}")
            print(f"  误差: {max_error_result['distance_error']:.2f} 像素")

    if failed_detections:
        print(f"\n检测失败的帧:")
        for i, result in enumerate(failed_detections[:5]):  # 只显示前5个
            print(f"  帧{result['frame']}: 真实位置 {result['true_position']}")
        if len(failed_detections) > 5:
            print(f"  ... 还有 {len(failed_detections)-5} 个失败帧")

def main():
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "compare":
        compare_with_annotations()
    else:
        test_annotation_based_detector()

if __name__ == "__main__":
    main()
