#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析视频中的激光点特征
"""

import cv2
import numpy as np
import os

def analyze_video_frames():
    """分析视频帧，保存一些样本帧用于分析"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    # 创建输出目录
    if not os.path.exists('video_frames'):
        os.makedirs('video_frames')
    
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"视频总帧数: {frame_count}")
    
    # 保存一些关键帧进行分析
    save_frames = [50, 100, 200, 300, 400, 500, 600]
    
    current_frame = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        current_frame += 1
        
        if current_frame in save_frames:
            # 保存原始帧
            cv2.imwrite(f'video_frames/frame_{current_frame:04d}.jpg', frame)
            
            # 分析帧的特征
            analyze_frame_features(frame, current_frame)
    
    cap.release()
    print("帧分析完成")

def analyze_frame_features(frame, frame_num):
    """分析单帧的特征"""
    print(f"\n=== 分析帧 {frame_num} ===")
    
    # 基本信息
    height, width = frame.shape[:2]
    print(f"分辨率: {width}x{height}")
    
    # 转换为不同颜色空间
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    
    # 分析亮度分布
    mean_brightness = np.mean(gray)
    max_brightness = np.max(gray)
    min_brightness = np.min(gray)
    print(f"亮度 - 平均: {mean_brightness:.1f}, 最大: {max_brightness}, 最小: {min_brightness}")
    
    # 查找最亮的区域
    bright_threshold = max(200, mean_brightness + 50)
    _, bright_mask = cv2.threshold(gray, bright_threshold, 255, cv2.THRESH_BINARY)
    bright_contours, _ = cv2.findContours(bright_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    print(f"亮度阈值 {bright_threshold} 下找到 {len(bright_contours)} 个亮区域")
    
    # 分析亮区域
    for i, contour in enumerate(bright_contours[:5]):  # 只分析前5个
        area = cv2.contourArea(contour)
        if area > 1:
            x, y, w, h = cv2.boundingRect(contour)
            print(f"  亮区域 {i+1}: 面积={area:.1f}, 位置=({x},{y}), 尺寸={w}x{h}")
    
    # 分析红色区域
    # 红色范围1
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    
    # 红色范围2
    lower_red2 = np.array([170, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    
    red_mask = cv2.bitwise_or(mask1, mask2)
    red_contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    print(f"找到 {len(red_contours)} 个红色区域")
    
    # 分析红色区域
    for i, contour in enumerate(red_contours[:5]):  # 只分析前5个
        area = cv2.contourArea(contour)
        if area > 1:
            x, y, w, h = cv2.boundingRect(contour)
            print(f"  红色区域 {i+1}: 面积={area:.1f}, 位置=({x},{y}), 尺寸={w}x{h}")
    
    # 保存分析结果图像
    analysis_img = frame.copy()
    
    # 标记亮区域
    for contour in bright_contours:
        if cv2.contourArea(contour) > 1:
            cv2.drawContours(analysis_img, [contour], -1, (0, 255, 0), 2)
    
    # 标记红色区域
    for contour in red_contours:
        if cv2.contourArea(contour) > 1:
            cv2.drawContours(analysis_img, [contour], -1, (255, 0, 0), 2)
    
    cv2.imwrite(f'video_frames/analysis_{frame_num:04d}.jpg', analysis_img)
    
    # 保存掩码图像
    cv2.imwrite(f'video_frames/bright_mask_{frame_num:04d}.jpg', bright_mask)
    cv2.imwrite(f'video_frames/red_mask_{frame_num:04d}.jpg', red_mask)

def find_potential_laser_points():
    """使用更宽松的条件查找潜在的激光点"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    frame_count = 0
    detection_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 使用多种方法尝试检测
        candidates = []
        
        # 方法1: 极亮点检测
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        max_val = np.max(gray)
        if max_val > 240:  # 如果有很亮的点
            _, thresh = cv2.threshold(gray, max_val - 10, 255, cv2.THRESH_BINARY)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            for contour in contours:
                area = cv2.contourArea(contour)
                if 1 <= area <= 100:
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        candidates.append((cx, cy, "bright"))
        
        # 方法2: 红色检测（更宽松的阈值）
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # 更宽松的红色范围
        lower_red1 = np.array([0, 30, 30])
        upper_red1 = np.array([20, 255, 255])
        lower_red2 = np.array([160, 30, 30])
        upper_red2 = np.array([180, 255, 255])
        
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(mask1, mask2)
        
        contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            area = cv2.contourArea(contour)
            if 1 <= area <= 200:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    candidates.append((cx, cy, "red"))
        
        # 方法3: 边缘检测
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        
        # 查找边缘中的小圆形区域
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            area = cv2.contourArea(contour)
            if 5 <= area <= 50:
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter ** 2)
                    if circularity > 0.5:  # 比较圆的形状
                        M = cv2.moments(contour)
                        if M["m00"] != 0:
                            cx = int(M["m10"] / M["m00"])
                            cy = int(M["m01"] / M["m00"])
                            candidates.append((cx, cy, "edge"))
        
        if candidates:
            detection_count += 1
            print(f"Frame {frame_count}: 找到 {len(candidates)} 个候选点")
            for i, (x, y, method) in enumerate(candidates):
                print(f"  候选点 {i+1}: ({x}, {y}) - 方法: {method}")
            
            # 保存有候选点的帧
            if frame_count <= 10:  # 只保存前10个有候选点的帧
                result_img = frame.copy()
                for x, y, method in candidates:
                    color = (0, 255, 0) if method == "bright" else (255, 0, 0) if method == "red" else (0, 0, 255)
                    cv2.circle(result_img, (x, y), 5, color, 2)
                    cv2.putText(result_img, method, (x+10, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
                
                cv2.imwrite(f'video_frames/candidates_{frame_count:04d}.jpg', result_img)
    
    cap.release()
    print(f"\n总结:")
    print(f"总帧数: {frame_count}")
    print(f"有候选点的帧数: {detection_count}")
    print(f"候选率: {detection_count/frame_count*100:.1f}%")

if __name__ == "__main__":
    print("开始分析视频...")
    analyze_video_frames()
    print("\n开始查找潜在激光点...")
    find_potential_laser_points()
