#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度激光点检测器 - 专注白色方形纸板区域
针对6Tops INT8算力设备，目标20FPS@640x480
使用多算法融合提高检测精度
"""

import cv2
import numpy as np
import time

def detect_laser_point_precision(image):
    """
    高精度激光点检测 - 专门针对白色方形纸板区域
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 步骤1: 检测白色纸板区域
    paper_region, roi_info = _extract_paper_region(image)
    
    if paper_region is None:
        # 如果无法检测纸板，使用全图
        paper_region = image
        roi_info = None
    
    # 步骤2: 多算法检测激光点
    candidates = []
    
    # 算法1: 增强亮度检测
    bright_candidates = _enhanced_brightness_detection(paper_region)
    candidates.extend(bright_candidates)
    
    # 算法2: 红色检测
    red_candidates = _enhanced_red_detection(paper_region)
    candidates.extend(red_candidates)
    
    # 算法3: 圆形检测
    circle_candidates = _hough_circle_detection(paper_region)
    candidates.extend(circle_candidates)
    
    # 算法4: 梯度检测
    gradient_candidates = _gradient_detection(paper_region)
    candidates.extend(gradient_candidates)
    
    # 步骤3: 智能选择最佳候选
    best_point = _select_best_candidate(paper_region, candidates)
    
    # 步骤4: 转换回原图坐标
    if best_point and roi_info:
        return _transform_to_original_coords(best_point, roi_info)
    
    return best_point

def _extract_paper_region(image):
    """提取白色纸板区域"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 多阈值检测白色区域
    _, binary1 = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)
    _, binary2 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 融合二值图
    binary = cv2.bitwise_or(binary1, binary2)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (10, 10))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 查找最大轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None, None
    
    # 找到最大的近似矩形轮廓
    max_area = 0
    best_contour = None
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 5000:  # 纸板应该足够大
            continue
        
        # 多边形逼近
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        if len(approx) == 4 and area > max_area:
            max_area = area
            best_contour = approx
    
    if best_contour is None:
        # 如果没找到四边形，使用最大轮廓的边界框
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        roi = image[y:y+h, x:x+w]
        return roi, {'type': 'bbox', 'x': x, 'y': y, 'w': w, 'h': h}
    
    # 透视变换提取纸板
    points = best_contour.reshape(4, 2).astype(np.float32)
    rect = _order_points(points)
    
    # 计算目标尺寸
    width = max(
        int(np.linalg.norm(rect[1] - rect[0])),
        int(np.linalg.norm(rect[2] - rect[3]))
    )
    height = max(
        int(np.linalg.norm(rect[3] - rect[0])),
        int(np.linalg.norm(rect[2] - rect[1]))
    )
    
    # 目标点
    dst = np.array([
        [0, 0], [width-1, 0], [width-1, height-1], [0, height-1]
    ], dtype=np.float32)
    
    # 透视变换
    M = cv2.getPerspectiveTransform(rect, dst)
    warped = cv2.warpPerspective(image, M, (width, height))
    
    return warped, {'type': 'perspective', 'matrix': M, 'original_points': rect}

def _order_points(pts):
    """排序四个角点"""
    rect = np.zeros((4, 2), dtype=np.float32)
    s = pts.sum(axis=1)
    diff = np.diff(pts, axis=1)
    
    rect[0] = pts[np.argmin(s)]      # 左上
    rect[2] = pts[np.argmax(s)]      # 右下
    rect[1] = pts[np.argmin(diff)]   # 右上
    rect[3] = pts[np.argmax(diff)]   # 左下
    
    return rect

def _enhanced_brightness_detection(image):
    """增强亮度检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 多阈值检测
    max_val = np.max(gray)
    for offset in [5, 10, 15, 20]:
        threshold = max(220, max_val - offset)
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 3 <= area <= 150:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 计算亮度得分
                    brightness_score = gray[cy, cx] / 255.0
                    area_score = min(area / 50.0, 1.0)
                    score = brightness_score * 0.7 + area_score * 0.3
                    
                    candidates.append({
                        'point': (cx, cy),
                        'score': score,
                        'method': 'brightness'
                    })
    
    return candidates

def _enhanced_red_detection(image):
    """增强红色检测"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    candidates = []
    
    # 多红色范围
    red_ranges = [
        ([0, 50, 50], [10, 255, 255]),
        ([170, 50, 50], [180, 255, 255]),
        ([0, 80, 120], [15, 255, 255]),
        ([165, 80, 120], [180, 255, 255])
    ]
    
    combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)
    
    for lower, upper in red_ranges:
        mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
        combined_mask = cv2.bitwise_or(combined_mask, mask)
    
    # LAB空间A通道检测
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    a_channel = lab[:, :, 1]
    a_normalized = cv2.normalize(a_channel, None, 0, 255, cv2.NORM_MINMAX)
    _, a_mask = cv2.threshold(a_normalized, 130, 255, cv2.THRESH_BINARY)
    
    # 融合掩码
    final_mask = cv2.bitwise_or(combined_mask, a_mask)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if 3 <= area <= 200:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                candidates.append({
                    'point': (cx, cy),
                    'score': area / 100.0,
                    'method': 'red'
                })
    
    return candidates

def _hough_circle_detection(image):
    """霍夫圆检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 多参数霍夫圆检测
    param_sets = [
        {'dp': 1, 'minDist': 15, 'param1': 50, 'param2': 20},
        {'dp': 1, 'minDist': 10, 'param1': 100, 'param2': 15},
        {'dp': 2, 'minDist': 20, 'param1': 50, 'param2': 25}
    ]
    
    for params in param_sets:
        circles = cv2.HoughCircles(
            gray, cv2.HOUGH_GRADIENT,
            dp=params['dp'], minDist=params['minDist'],
            param1=params['param1'], param2=params['param2'],
            minRadius=2, maxRadius=15
        )
        
        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")
            for (x, y, r) in circles:
                if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                    # 计算圆形得分
                    center_val = gray[y, x]
                    score = center_val / 255.0
                    candidates.append({
                        'point': (x, y),
                        'score': score,
                        'method': 'circle',
                        'radius': r
                    })
    
    return candidates

def _gradient_detection(image):
    """梯度检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 计算梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    magnitude = np.sqrt(grad_x**2 + grad_y**2)
    
    # 查找局部最大值
    kernel = np.ones((5, 5))
    local_max = cv2.dilate(magnitude, kernel) == magnitude
    
    # 阈值筛选
    threshold = np.percentile(magnitude, 95)
    peaks = local_max & (magnitude > threshold)
    
    # 提取候选点
    y_coords, x_coords = np.where(peaks)
    for x, y in zip(x_coords, y_coords):
        score = magnitude[y, x] / np.max(magnitude)
        candidates.append({
            'point': (x, y),
            'score': score,
            'method': 'gradient'
        })
    
    return candidates

def _select_best_candidate(image, candidates):
    """选择最佳候选点"""
    if not candidates:
        return None
    
    if len(candidates) == 1:
        return candidates[0]['point']
    
    # 聚类相近的候选点
    clusters = _simple_clustering(candidates, distance_threshold=15)
    
    # 评估每个聚类
    best_cluster = None
    best_score = 0
    
    for cluster in clusters:
        score = _evaluate_cluster_simple(image, cluster)
        if score > best_score:
            best_score = score
            best_cluster = cluster
    
    if best_cluster:
        # 返回聚类中心
        points = [c['point'] for c in best_cluster]
        center_x = int(np.mean([p[0] for p in points]))
        center_y = int(np.mean([p[1] for p in points]))
        return (center_x, center_y)
    
    return None

def _simple_clustering(candidates, distance_threshold):
    """简单聚类算法"""
    clusters = []
    used = [False] * len(candidates)
    
    for i, candidate in enumerate(candidates):
        if used[i]:
            continue
        
        cluster = [candidate]
        used[i] = True
        point = candidate['point']
        
        for j, other_candidate in enumerate(candidates):
            if used[j]:
                continue
            
            other_point = other_candidate['point']
            distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
            
            if distance <= distance_threshold:
                cluster.append(other_candidate)
                used[j] = True
        
        clusters.append(cluster)
    
    return clusters

def _evaluate_cluster_simple(image, cluster):
    """简单聚类评估"""
    if not cluster:
        return 0
    
    # 基础得分
    avg_score = np.mean([c['score'] for c in cluster])
    num_candidates = len(cluster)
    
    # 方法多样性
    methods = set(c['method'] for c in cluster)
    diversity_bonus = len(methods) * 0.1
    
    # 综合得分
    total_score = avg_score * 0.7 + diversity_bonus + min(num_candidates / 5, 0.2)
    
    return total_score

def _transform_to_original_coords(point, roi_info):
    """转换坐标回原图"""
    if not roi_info:
        return point
    
    x, y = point
    
    if roi_info['type'] == 'bbox':
        # 边界框变换
        return (x + roi_info['x'], y + roi_info['y'])
    
    elif roi_info['type'] == 'perspective':
        # 透视变换
        inv_matrix = cv2.invert(roi_info['matrix'])[1]
        point_array = np.array([[[x, y]]], dtype=np.float32)
        transformed = cv2.perspectiveTransform(point_array, inv_matrix)
        new_x, new_y = transformed[0][0]
        return (int(new_x), int(new_y))
    
    return point

def test_precision_detector():
    """测试高精度检测器"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("开始高精度激光点检测测试...")
    print("目标: 20 FPS @ 640x480 分辨率")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 调整到目标分辨率
        frame = cv2.resize(frame, (640, 480))
        frame = frame[65:410, 260:470, :]
        
        start_time = time.time()
        
        # 高精度检测
        laser_point = detect_laser_point_precision(frame)
        
        end_time = time.time()
        total_time += (end_time - start_time)
        
        if laser_point:
            detection_count += 1
            x, y = laser_point
            
            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y})")
            
            # 绘制检测结果
            cv2.circle(frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # 显示FPS
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # 显示性能状态
            status = "OK" if current_fps >= 20 else "LOW"
            color = (0, 255, 0) if current_fps >= 20 else (0, 0, 255)
            cv2.putText(frame, f"Status: {status}", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        cv2.imshow('Precision Laser Detection', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 高精度检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")
    print(f"目标性能: 20 FPS @ 640x480")
    print(f"性能达标: {'✓' if avg_fps >= 20 else '✗'}")

if __name__ == "__main__":
    print("高精度激光点检测器")
    print("专注白色方形纸板区域，多算法融合")
    print("目标: 20 FPS @ 640x480 分辨率")
    test_precision_detector()
