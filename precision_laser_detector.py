#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度激光点检测器 - 专注白色方形纸板区域
针对6Tops INT8算力设备，目标20FPS@640x480
使用多算法融合提高检测精度
"""

import cv2
import numpy as np
import time

def detect_laser_point_precision(image):
    """
    高精度激光点检测 - 专门针对白色方形纸板区域
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 步骤1: 检测白色纸板区域
    paper_region, roi_info = _extract_paper_region(image)
    
    if paper_region is None:
        # 如果无法检测纸板，使用全图
        paper_region = image
        roi_info = None
    
    # 步骤2: 多算法检测激光点
    candidates = []
    
    # 算法1: 增强亮度检测
    bright_candidates = _enhanced_brightness_detection(paper_region)
    candidates.extend(bright_candidates)
    
    # 算法2: 红色检测
    red_candidates = _enhanced_red_detection(paper_region)
    candidates.extend(red_candidates)
    
    # 算法3: 圆形检测
    circle_candidates = _hough_circle_detection(paper_region)
    candidates.extend(circle_candidates)
    
    # 算法4: 梯度检测
    gradient_candidates = _gradient_detection(paper_region)
    candidates.extend(gradient_candidates)
    
    # 步骤3: 智能选择最佳候选
    best_point = _select_best_candidate(paper_region, candidates)
    
    # 步骤4: 转换回原图坐标
    if best_point and roi_info:
        return _transform_to_original_coords(best_point, roi_info)
    
    return best_point

def _extract_paper_region(image):
    """提取白色纸板区域"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 多阈值检测白色区域
    _, binary1 = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)
    _, binary2 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 融合二值图
    binary = cv2.bitwise_or(binary1, binary2)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (10, 10))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 查找最大轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None, None
    
    # 找到最大的近似矩形轮廓
    max_area = 0
    best_contour = None
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 5000:  # 纸板应该足够大
            continue
        
        # 多边形逼近
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        if len(approx) == 4 and area > max_area:
            max_area = area
            best_contour = approx
    
    if best_contour is None:
        # 如果没找到四边形，使用最大轮廓的边界框
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        roi = image[y:y+h, x:x+w]
        return roi, {'type': 'bbox', 'x': x, 'y': y, 'w': w, 'h': h}
    
    # 透视变换提取纸板
    points = best_contour.reshape(4, 2).astype(np.float32)
    rect = _order_points(points)
    
    # 计算目标尺寸
    width = max(
        int(np.linalg.norm(rect[1] - rect[0])),
        int(np.linalg.norm(rect[2] - rect[3]))
    )
    height = max(
        int(np.linalg.norm(rect[3] - rect[0])),
        int(np.linalg.norm(rect[2] - rect[1]))
    )
    
    # 目标点
    dst = np.array([
        [0, 0], [width-1, 0], [width-1, height-1], [0, height-1]
    ], dtype=np.float32)
    
    # 透视变换
    M = cv2.getPerspectiveTransform(rect, dst)
    warped = cv2.warpPerspective(image, M, (width, height))
    
    return warped, {'type': 'perspective', 'matrix': M, 'original_points': rect}

def _order_points(pts):
    """排序四个角点"""
    rect = np.zeros((4, 2), dtype=np.float32)
    s = pts.sum(axis=1)
    diff = np.diff(pts, axis=1)
    
    rect[0] = pts[np.argmin(s)]      # 左上
    rect[2] = pts[np.argmax(s)]      # 右下
    rect[1] = pts[np.argmin(diff)]   # 右上
    rect[3] = pts[np.argmax(diff)]   # 左下
    
    return rect

def _enhanced_brightness_detection(image):
    """增强亮度检测 - 添加更严格的筛选条件"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []

    # 计算图像统计信息
    mean_brightness = np.mean(gray)
    max_val = np.max(gray)

    # 如果最大亮度不够突出，跳过检测
    if max_val < 230 or (max_val - mean_brightness) < 50:
        return candidates

    # 多阈值检测，使用更严格的阈值
    for offset in [3, 6, 10]:
        threshold = max(240, max_val - offset)
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)

        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        for contour in contours:
            area = cv2.contourArea(contour)
            if 2 <= area <= 80:  # 更严格的面积限制
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])

                    # 更严格的特征验证
                    if _verify_laser_point_features(gray, (cx, cy)):
                        brightness_score = gray[cy, cx] / 255.0
                        area_score = min(area / 30.0, 1.0)

                        # 计算圆形度
                        perimeter = cv2.arcLength(contour, True)
                        if perimeter > 0:
                            circularity = 4 * np.pi * area / (perimeter ** 2)
                            circularity_score = min(circularity, 1.0)
                        else:
                            circularity_score = 0

                        # 综合得分，增加圆形度权重
                        score = (brightness_score * 0.5 +
                                area_score * 0.2 +
                                circularity_score * 0.3)

                        # 只保留高质量候选点
                        if score > 0.6:
                            candidates.append({
                                'point': (cx, cy),
                                'score': score,
                                'method': 'brightness',
                                'circularity': circularity_score
                            })

    return candidates

def _verify_laser_point_features(gray, point):
    """验证激光点特征 - 更严格的验证"""
    x, y = point
    h, w = gray.shape

    # 边界检查
    if x < 5 or y < 5 or x >= w-5 or y >= h-5:
        return False

    # 中心亮度检查
    center_val = gray[y, x]
    if center_val < 240:  # 激光点应该非常亮
        return False

    # 局部对比度检查
    local_region = gray[y-3:y+4, x-3:x+4]
    avg_surrounding = np.mean(local_region)
    contrast = center_val - avg_surrounding

    if contrast < 30:  # 对比度不够
        return False

    # 检查是否为孤立的亮点（周围不应该有太多亮点）
    bright_neighbors = np.sum(local_region > 200)
    if bright_neighbors > 15:  # 如果周围太多亮点，可能是噪声
        return False

    # 检查亮度梯度（激光点中心应该是最亮的）
    center_3x3 = gray[y-1:y+2, x-1:x+2]
    if center_val < np.max(center_3x3):  # 中心不是最亮的
        return False

    return True

def _enhanced_red_detection(image):
    """增强红色检测 - 添加更严格的筛选条件"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []

    # 更精确的红色范围，提高饱和度和亮度要求
    red_ranges = [
        ([0, 100, 150], [8, 255, 255]),      # 红色范围1，更高要求
        ([172, 100, 150], [180, 255, 255]),  # 红色范围2，更高要求
    ]

    combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)

    for lower, upper in red_ranges:
        mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
        combined_mask = cv2.bitwise_or(combined_mask, mask)

    # LAB空间A通道检测，提高阈值
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    a_channel = lab[:, :, 1]
    a_normalized = cv2.normalize(a_channel, None, 0, 255, cv2.NORM_MINMAX)
    _, a_mask = cv2.threshold(a_normalized, 145, 255, cv2.THRESH_BINARY)  # 提高阈值

    # 亮度掩码 - 激光点应该足够亮
    _, brightness_mask = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)

    # 融合所有掩码
    final_mask = cv2.bitwise_and(combined_mask, a_mask)
    final_mask = cv2.bitwise_and(final_mask, brightness_mask)

    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        area = cv2.contourArea(contour)
        if 2 <= area <= 100:  # 更严格的面积限制
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])

                # 验证红色激光点特征
                if _verify_red_laser_features(image, (cx, cy)):
                    # 计算圆形度
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        circularity = 4 * np.pi * area / (perimeter ** 2)

                        # 只保留圆形度较好的候选点
                        if circularity > 0.4:
                            score = min(area / 50.0, 1.0) * circularity
                            candidates.append({
                                'point': (cx, cy),
                                'score': score,
                                'method': 'red',
                                'circularity': circularity
                            })

    return candidates

def _verify_red_laser_features(image, point):
    """验证红色激光点特征"""
    x, y = point
    h, w = image.shape[:2]

    # 边界检查
    if x < 3 or y < 3 or x >= w-3 or y >= h-3:
        return False

    # 提取BGR值
    b, g, r = image[y, x]

    # 红色激光点应该红色分量明显高于其他分量
    if r < 150 or r < g + 30 or r < b + 30:
        return False

    # 检查HSV值
    hsv = cv2.cvtColor(image[y-1:y+2, x-1:x+2], cv2.COLOR_BGR2HSV)
    center_hsv = hsv[1, 1]
    h_val, s_val, v_val = center_hsv

    # HSV验证
    if not ((0 <= h_val <= 8) or (172 <= h_val <= 180)):  # 红色色调
        return False
    if s_val < 100:  # 饱和度要求
        return False
    if v_val < 150:  # 亮度要求
        return False

    return True

def _hough_circle_detection(image):
    """霍夫圆检测 - 更严格的参数和验证"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []

    # 预处理：高斯模糊减少噪声
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)

    # 更严格的参数设置
    param_sets = [
        {'dp': 1, 'minDist': 12, 'param1': 80, 'param2': 25},
        {'dp': 1, 'minDist': 8, 'param1': 100, 'param2': 20},
    ]

    for params in param_sets:
        circles = cv2.HoughCircles(
            blurred, cv2.HOUGH_GRADIENT,
            dp=params['dp'], minDist=params['minDist'],
            param1=params['param1'], param2=params['param2'],
            minRadius=1, maxRadius=10  # 更小的半径范围
        )

        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")
            for (x, y, r) in circles:
                if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                    # 验证圆形特征
                    if _verify_circle_features(gray, (x, y), r):
                        center_val = gray[y, x]
                        brightness_score = center_val / 255.0

                        # 计算圆形质量得分
                        circle_quality = _calculate_circle_quality(gray, (x, y), r)

                        score = brightness_score * 0.6 + circle_quality * 0.4

                        # 只保留高质量的圆
                        if score > 0.7:
                            candidates.append({
                                'point': (x, y),
                                'score': score,
                                'method': 'circle',
                                'radius': r
                            })

    return candidates

def _verify_circle_features(gray, center, radius):
    """验证圆形特征"""
    x, y = center
    h, w = gray.shape

    # 边界检查
    if x < radius+2 or y < radius+2 or x >= w-radius-2 or y >= h-radius-2:
        return False

    # 中心亮度检查
    center_val = gray[y, x]
    if center_val < 220:
        return False

    # 检查圆周上的点
    angles = np.linspace(0, 2*np.pi, 12, endpoint=False)
    circle_vals = []

    for angle in angles:
        px = int(x + radius * np.cos(angle))
        py = int(y + radius * np.sin(angle))
        if 0 <= px < w and 0 <= py < h:
            circle_vals.append(gray[py, px])

    if not circle_vals:
        return False

    # 圆周应该比中心暗
    avg_circle = np.mean(circle_vals)
    if center_val - avg_circle < 20:
        return False

    return True

def _calculate_circle_quality(gray, center, radius):
    """计算圆形质量得分"""
    x, y = center
    h, w = gray.shape

    if x < radius+1 or y < radius+1 or x >= w-radius-1 or y >= h-radius-1:
        return 0

    # 检查多个半径的圆周
    quality_scores = []

    for r in range(max(1, radius-1), radius+2):
        angles = np.linspace(0, 2*np.pi, 16, endpoint=False)
        circle_vals = []

        for angle in angles:
            px = int(x + r * np.cos(angle))
            py = int(y + r * np.sin(angle))
            if 0 <= px < w and 0 <= py < h:
                circle_vals.append(gray[py, px])

        if circle_vals:
            # 计算圆周亮度的一致性
            std_dev = np.std(circle_vals)
            consistency = 1.0 / (1.0 + std_dev / 50.0)
            quality_scores.append(consistency)

    return np.mean(quality_scores) if quality_scores else 0

def _gradient_detection(image):
    """梯度检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 计算梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    magnitude = np.sqrt(grad_x**2 + grad_y**2)
    
    # 查找局部最大值
    kernel = np.ones((5, 5))
    local_max = cv2.dilate(magnitude, kernel) == magnitude
    
    # 阈值筛选
    threshold = np.percentile(magnitude, 95)
    peaks = local_max & (magnitude > threshold)
    
    # 提取候选点
    y_coords, x_coords = np.where(peaks)
    for x, y in zip(x_coords, y_coords):
        score = magnitude[y, x] / np.max(magnitude)
        candidates.append({
            'point': (x, y),
            'score': score,
            'method': 'gradient'
        })
    
    return candidates

def _select_best_candidate(image, candidates):
    """选择最佳候选点 - 更严格的筛选"""
    if not candidates:
        return None

    # 首先过滤低质量候选点
    high_quality_candidates = []
    for candidate in candidates:
        if candidate['score'] > 0.5:  # 只保留高得分候选点
            high_quality_candidates.append(candidate)

    if not high_quality_candidates:
        return None

    if len(high_quality_candidates) == 1:
        return high_quality_candidates[0]['point']

    # 聚类相近的候选点
    clusters = _simple_clustering(high_quality_candidates, distance_threshold=12)

    # 评估每个聚类
    best_cluster = None
    best_score = 0

    for cluster in clusters:
        score = _evaluate_cluster_advanced(image, cluster)
        if score > best_score:
            best_score = score
            best_cluster = cluster

    if best_cluster and best_score > 0.6:  # 只返回高质量聚类
        # 返回聚类中心，使用加权平均
        total_weight = sum(c['score'] for c in best_cluster)
        if total_weight > 0:
            weighted_x = sum(c['point'][0] * c['score'] for c in best_cluster) / total_weight
            weighted_y = sum(c['point'][1] * c['score'] for c in best_cluster) / total_weight
            return (int(weighted_x), int(weighted_y))
        else:
            points = [c['point'] for c in best_cluster]
            center_x = int(np.mean([p[0] for p in points]))
            center_y = int(np.mean([p[1] for p in points]))
            return (center_x, center_y)

    return None

def _evaluate_cluster_advanced(image, cluster):
    """高级聚类评估"""
    if not cluster:
        return 0

    # 基础得分
    scores = [c['score'] for c in cluster]
    avg_score = np.mean(scores)
    max_score = np.max(scores)

    # 方法多样性奖励
    methods = set(c['method'] for c in cluster)
    diversity_bonus = len(methods) * 0.15

    # 位置一致性
    points = [c['point'] for c in cluster]
    if len(points) > 1:
        center = np.mean(points, axis=0)
        distances = [np.sqrt((p[0] - center[0])**2 + (p[1] - center[1])**2) for p in points]
        consistency = 1.0 / (1.0 + np.std(distances))
    else:
        consistency = 1.0

    # 圆形度奖励（如果有的话）
    circularity_bonus = 0
    if any('circularity' in c for c in cluster):
        circularities = [c.get('circularity', 0) for c in cluster]
        avg_circularity = np.mean([c for c in circularities if c > 0])
        if avg_circularity > 0:
            circularity_bonus = avg_circularity * 0.2

    # 图像特征验证
    center_point = tuple(np.mean(points, axis=0).astype(int))
    feature_score = _advanced_feature_verification(image, center_point)

    # 综合得分
    total_score = (avg_score * 0.4 +
                  max_score * 0.2 +
                  diversity_bonus +
                  consistency * 0.15 +
                  circularity_bonus +
                  feature_score * 0.25)

    return total_score

def _advanced_feature_verification(image, point):
    """高级特征验证"""
    x, y = point
    h, w = image.shape[:2]

    if x < 5 or y < 5 or x >= w-5 or y >= h-5:
        return 0

    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 亮度特征
    center_val = gray[y, x]
    if center_val < 230:  # 激光点应该非常亮
        return 0

    # 局部对比度
    local_region = gray[y-4:y+5, x-4:x+5]
    avg_local = np.mean(local_region)
    contrast = (center_val - avg_local) / 255.0

    # 梯度特征
    grad_x = cv2.Sobel(local_region, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(local_region, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(grad_x[4, 4]**2 + grad_y[4, 4]**2) / 255.0

    # 圆形度检查
    radius = 3
    angles = np.linspace(0, 2*np.pi, 12, endpoint=False)
    circle_vals = []

    for angle in angles:
        px = int(x + radius * np.cos(angle))
        py = int(y + radius * np.sin(angle))
        if 0 <= px < w and 0 <= py < h:
            circle_vals.append(gray[py, px])

    if circle_vals:
        circle_std = np.std(circle_vals)
        circularity_score = 1.0 / (1.0 + circle_std / 30.0)
    else:
        circularity_score = 0

    # 综合特征得分
    feature_score = (contrast * 0.4 +
                    gradient_magnitude * 0.3 +
                    circularity_score * 0.3)

    return max(0, min(1, feature_score))

def _simple_clustering(candidates, distance_threshold):
    """简单聚类算法"""
    clusters = []
    used = [False] * len(candidates)
    
    for i, candidate in enumerate(candidates):
        if used[i]:
            continue
        
        cluster = [candidate]
        used[i] = True
        point = candidate['point']
        
        for j, other_candidate in enumerate(candidates):
            if used[j]:
                continue
            
            other_point = other_candidate['point']
            distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
            
            if distance <= distance_threshold:
                cluster.append(other_candidate)
                used[j] = True
        
        clusters.append(cluster)
    
    return clusters



def _transform_to_original_coords(point, roi_info):
    """转换坐标回原图"""
    if not roi_info:
        return point
    
    x, y = point
    
    if roi_info['type'] == 'bbox':
        # 边界框变换
        return (x + roi_info['x'], y + roi_info['y'])
    
    elif roi_info['type'] == 'perspective':
        # 透视变换
        inv_matrix = cv2.invert(roi_info['matrix'])[1]
        point_array = np.array([[[x, y]]], dtype=np.float32)
        transformed = cv2.perspectiveTransform(point_array, inv_matrix)
        new_x, new_y = transformed[0][0]
        return (int(new_x), int(new_y))
    
    return point

def test_precision_detector():
    """测试高精度检测器"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("开始高精度激光点检测测试...")
    print("目标: 20 FPS @ 640x480 分辨率")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 调整到目标分辨率
        frame = cv2.resize(frame, (640, 480))

        # 保存原始帧用于显示
        display_frame = frame.copy()

        # 提取ROI区域
        roi_frame = frame[65:410, 260:470, :]
        roi_offset = (260, 65)  # (x_offset, y_offset)

        start_time = time.time()

        # 高精度检测
        laser_point = detect_laser_point_precision(roi_frame)

        end_time = time.time()
        total_time += (end_time - start_time)

        if laser_point:
            detection_count += 1
            # 转换回原图坐标
            x = laser_point[0] + roi_offset[0]
            y = laser_point[1] + roi_offset[1]

            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y}) [ROI: ({laser_point[0]}, {laser_point[1]})]")

            # 在原图上绘制检测结果
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(display_frame, f"({x}, {y})", (x+15, y-15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # 绘制ROI区域边界
            cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
            cv2.putText(display_frame, "ROI", (265, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 显示FPS和状态信息
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(display_frame, f"FPS: {current_fps:.1f}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

            # 显示性能状态
            status = "OK" if current_fps >= 20 else "LOW"
            color = (0, 255, 0) if current_fps >= 20 else (0, 0, 255)
            cv2.putText(display_frame, f"Status: {status}", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

            # 显示检测信息
            detection_rate = detection_count / frame_count * 100
            cv2.putText(display_frame, f"Detection: {detection_rate:.1f}%", (10, 90),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        cv2.imshow('Enhanced Precision Laser Detection', display_frame)

        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s') and laser_point:
            # 保存当前帧
            filename = f"enhanced_precision_{frame_count:04d}.jpg"
            cv2.imwrite(filename, display_frame)
            print(f"保存图像: {filename}")
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 高精度检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")
    print(f"目标性能: 20 FPS @ 640x480")
    print(f"性能达标: {'✓' if avg_fps >= 20 else '✗'}")

if __name__ == "__main__":
    print("高精度激光点检测器")
    print("专注白色方形纸板区域，多算法融合")
    print("目标: 20 FPS @ 640x480 分辨率")
    test_precision_detector()
