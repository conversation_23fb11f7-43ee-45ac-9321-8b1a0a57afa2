#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
推荐的激光点检测器
基于标注数据分析，结合高检测率和合理精度
专为您的应用场景优化
"""

import cv2
import numpy as np
import time

class RecommendedLaserDetector:
    def __init__(self):
        # 基于分析结果的优化参数
        self.params = {
            'min_brightness': 130,
            'brightness_threshold_offset': 20,
            'min_contrast': 5,
            'min_area': 1,
            'max_area': 180,
            'min_circularity': 0.15,
            'score_threshold': 0.25
        }
        print("推荐检测器已初始化")
    
    def detect_laser_point(self, image):
        """检测激光点 - 平衡检测率和精度"""
        if image is None:
            return None
        
        candidates = []
        
        # 主要方法：多阈值亮度检测
        bright_candidates = self._multi_threshold_detection(image)
        candidates.extend(bright_candidates)
        
        # 辅助方法：红色检测
        if len(candidates) < 3:
            red_candidates = self._red_detection(image)
            candidates.extend(red_candidates)
        
        # 备用方法：形态学检测
        if len(candidates) < 2:
            morph_candidates = self._morphological_detection(image)
            candidates.extend(morph_candidates)
        
        # 智能选择
        return self._intelligent_selection(candidates)
    
    def _multi_threshold_detection(self, image):
        """多阈值亮度检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        max_val = np.max(gray)
        mean_val = np.mean(gray)
        
        # 使用多个阈值策略
        thresholds = [
            max_val - 15,  # 最亮区域
            max_val - 25,  # 次亮区域
            max_val - 35,  # 较亮区域
            mean_val + 40, # 高于平均值
            mean_val + 25  # 中等亮度
        ]
        
        for i, threshold in enumerate(thresholds):
            if threshold < self.params['min_brightness']:
                continue
            
            _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
            
            # 轻微形态学操作
            if i < 2:  # 对最亮的区域使用更严格的形态学操作
                kernel = np.ones((3, 3), np.uint8)
                binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.params['min_area'] <= area <= self.params['max_area']:
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        if self._verify_point(gray, (cx, cy)):
                            # 计算综合得分
                            brightness_score = gray[cy, cx] / 255.0
                            area_score = min(area / 50.0, 1.0)
                            
                            # 圆形度
                            perimeter = cv2.arcLength(contour, True)
                            if perimeter > 0:
                                circularity = 4 * np.pi * area / (perimeter ** 2)
                            else:
                                circularity = 0
                            
                            # 阈值权重（越严格的阈值权重越高）
                            threshold_weight = 1.0 - i * 0.1
                            
                            score = (brightness_score * 0.4 + 
                                   area_score * 0.2 + 
                                   circularity * 0.2 + 
                                   threshold_weight * 0.2)
                            
                            if score >= self.params['score_threshold']:
                                candidates.append({
                                    'point': (cx, cy),
                                    'score': score,
                                    'method': 'brightness',
                                    'threshold_level': i
                                })
        
        return candidates
    
    def _red_detection(self, image):
        """红色检测"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 红色范围
        red_ranges = [
            ([0, 50, 100], [12, 255, 255]),
            ([168, 50, 100], [180, 255, 255])
        ]
        
        combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)
        
        for lower, upper in red_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # 亮度掩码
        _, brightness_mask = cv2.threshold(gray, self.params['min_brightness'], 255, cv2.THRESH_BINARY)
        
        # 融合掩码
        final_mask = cv2.bitwise_and(combined_mask, brightness_mask)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.params['min_area'] <= area <= self.params['max_area']:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    score = min(area / 60.0, 1.0) * 0.8  # 红色检测得分稍低
                    candidates.append({
                        'point': (cx, cy),
                        'score': score,
                        'method': 'red'
                    })
        
        return candidates
    
    def _morphological_detection(self, image):
        """形态学检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 顶帽变换检测小的亮点
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        tophat = cv2.morphologyEx(gray, cv2.MORPH_TOPHAT, kernel)
        
        # 阈值化
        _, binary = cv2.threshold(tophat, 20, 255, cv2.THRESH_BINARY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 3 <= area <= 100:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    if gray[cy, cx] > self.params['min_brightness']:
                        score = gray[cy, cx] / 255.0 * 0.6  # 形态学检测得分较低
                        candidates.append({
                            'point': (cx, cy),
                            'score': score,
                            'method': 'morphology'
                        })
        
        return candidates
    
    def _verify_point(self, gray, point):
        """验证点的特征"""
        x, y = point
        h, w = gray.shape
        
        if x < 4 or y < 4 or x >= w-4 or y >= h-4:
            return False
        
        center_val = gray[y, x]
        if center_val < self.params['min_brightness']:
            return False
        
        # 对比度检查
        local_region = gray[y-3:y+4, x-3:x+4]
        avg_surrounding = np.mean(local_region)
        contrast = center_val - avg_surrounding
        
        if contrast < self.params['min_contrast']:
            return False
        
        return True
    
    def _intelligent_selection(self, candidates):
        """智能选择最佳候选点"""
        if not candidates:
            return None
        
        if len(candidates) == 1:
            return candidates[0]['point']
        
        # 按得分排序
        candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)
        
        # 聚类分析
        clusters = self._cluster_candidates(candidates)
        
        # 评估聚类
        best_cluster = None
        best_score = 0
        
        for cluster in clusters:
            cluster_score = self._evaluate_cluster(cluster)
            if cluster_score > best_score:
                best_score = cluster_score
                best_cluster = cluster
        
        if best_cluster:
            # 返回加权中心
            total_weight = sum(c['score'] for c in best_cluster)
            if total_weight > 0:
                weighted_x = sum(c['point'][0] * c['score'] for c in best_cluster) / total_weight
                weighted_y = sum(c['point'][1] * c['score'] for c in best_cluster) / total_weight
                return (int(weighted_x), int(weighted_y))
        
        return candidates[0]['point']
    
    def _cluster_candidates(self, candidates, distance_threshold=10):
        """聚类候选点"""
        clusters = []
        used = [False] * len(candidates)
        
        for i, candidate in enumerate(candidates):
            if used[i]:
                continue
            
            cluster = [candidate]
            used[i] = True
            point = candidate['point']
            
            for j, other_candidate in enumerate(candidates):
                if used[j]:
                    continue
                
                other_point = other_candidate['point']
                distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
                
                if distance <= distance_threshold:
                    cluster.append(other_candidate)
                    used[j] = True
            
            clusters.append(cluster)
        
        return clusters
    
    def _evaluate_cluster(self, cluster):
        """评估聚类质量"""
        if not cluster:
            return 0
        
        # 基础得分
        avg_score = np.mean([c['score'] for c in cluster])
        max_score = np.max([c['score'] for c in cluster])
        
        # 方法多样性
        methods = set(c['method'] for c in cluster)
        diversity_bonus = len(methods) * 0.1
        
        # 候选点数量
        count_bonus = min(len(cluster) / 4, 0.2)
        
        # 亮度检测优先级
        brightness_count = len([c for c in cluster if c['method'] == 'brightness'])
        brightness_bonus = min(brightness_count / len(cluster), 0.3)
        
        return avg_score * 0.4 + max_score * 0.3 + diversity_bonus + count_bonus + brightness_bonus

class LaserTracker:
    """激光点跟踪器"""
    
    def __init__(self, history_size=5):
        self.history = []
        self.history_size = history_size
        
    def update(self, detection):
        """更新检测结果"""
        if detection is not None:
            self.history.append(detection)
            if len(self.history) > self.history_size:
                self.history.pop(0)
            
            # 返回平滑后的位置
            if len(self.history) >= 3:
                # 使用加权平均，最新的检测权重更高
                weights = np.linspace(0.5, 1.0, len(self.history))
                weights = weights / np.sum(weights)
                
                avg_x = sum(p[0] * w for p, w in zip(self.history, weights))
                avg_y = sum(p[1] * w for p, w in zip(self.history, weights))
                return (int(avg_x), int(avg_y))
            else:
                return detection
        else:
            # 逐渐减少历史记录
            if self.history:
                self.history.pop(0)
            return self.history[-1] if self.history else None

def test_recommended_detector():
    """测试推荐检测器"""
    detector = RecommendedLaserDetector()
    tracker = LaserTracker()
    
    # cap = cv2.VideoCapture('output.mp4')
    cap = cv2.VideoCapture(0)
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("开始测试推荐检测器...")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        frame = cv2.resize(frame, (640, 480))
        
        # 保存原始帧用于显示
        display_frame = frame.copy()
        
        # 提取ROI区域
        roi_frame = frame[65:410, 260:470, :]
        roi_offset = (260, 65)
        
        start_time = time.time()
        
        # 检测激光点
        laser_point = detector.detect_laser_point(roi_frame)
        
        # 使用跟踪器稳定结果
        if laser_point:
            original_point = (laser_point[0] + roi_offset[0], laser_point[1] + roi_offset[1])
            tracked_point = tracker.update(original_point)
        else:
            tracked_point = tracker.update(None)
        
        end_time = time.time()
        total_time += (end_time - start_time)
        
        if tracked_point:
            detection_count += 1
            x, y = tracked_point
            
            if frame_count <= 20 or frame_count % 50 == 0:
                roi_x = laser_point[0] if laser_point else "N/A"
                roi_y = laser_point[1] if laser_point else "N/A"
                print(f"Frame {frame_count}: 激光点 ({x}, {y}) [ROI: ({roi_x}, {roi_y})]")
            
            # 绘制检测结果
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(display_frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 绘制ROI区域
            cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
            cv2.putText(display_frame, "ROI", (265, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 显示信息
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(display_frame, f"FPS: {current_fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            detection_rate = detection_count / frame_count * 100
            cv2.putText(display_frame, f"Detection: {detection_rate:.1f}%", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 显示结果
        if frame_count % 3 == 0:
            cv2.imshow('Recommended Laser Detection', display_frame)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s') and tracked_point:
                filename = f"recommended_detection_{frame_count:04d}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"保存图像: {filename}")
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 推荐检测器结果 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")

if __name__ == "__main__":
    test_recommended_detector()
