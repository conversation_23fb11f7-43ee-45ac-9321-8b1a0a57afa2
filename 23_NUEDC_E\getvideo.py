# 250217

import cv2
import time

pre_time = time.perf_counter_ns()

vc = cv2.VideoCapture(1)
# vc = cv2.VideoCapture(r"bilicv/cv01.mp4")

vc.set(cv2.CAP_PROP_FRAME_WIDTH, 2560)
vc.set(cv2.CAP_PROP_FRAME_HEIGHT, 1440)

frame_width = int(vc.get(cv2.CAP_PROP_FRAME_WIDTH))
frame_height = int(vc.get(cv2.CAP_PROP_FRAME_HEIGHT))
fps = vc.get(cv2.CAP_PROP_FPS)

print(frame_height, frame_width)
 
# 创建VideoWriter对象
fourcc = cv2.VideoWriter_fourcc(*'H264')
# fourcc = cv2.VideoWriter_fourcc(*'MJPG')
out = cv2.VideoWriter('output.mp4', fourcc, fps, (frame_width, frame_height))

if not out.isOpened():
    print("Error: Could not open the output video for write")

# 检查打开是否正确
if vc.isOpened():
    open, frame = vc.read()
else:
    open = False
    print('False')

while open:
    ret, frame = vc.read()
    real_time = time.perf_counter_ns()
    if frame is None:
        break
    if ret is True:
        fps = 1000000000 / (real_time - pre_time)
        out.write(frame)

        cv2.putText(frame, str(fps), (0, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.65, (0, 255, 0), 1)
        cv2.imshow('result', frame)
        pre_time = time.perf_counter_ns()
        if cv2.waitKey(1) & 0xFF == 27:
            break
vc.release()
cv2.destroyAllWindows()
