#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强型激光点检测器 - 专注白色方形纸板区域
仅使用OpenCV和NumPy，避免依赖问题
针对6Tops INT8算力设备优化，目标20FPS@640x480
"""

import cv2
import numpy as np
import time

def detect_laser_point_enhanced(image):
    """
    增强型激光点检测 - 专门针对白色方形纸板区域
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 步骤1: 检测并提取白色方形纸板区域
    paper_region, paper_mask, transform_info = _detect_and_extract_paper(image)
    
    if paper_region is None:
        # 如果无法检测到纸板，使用全图检测
        paper_region = image
        paper_mask = np.ones(image.shape[:2], dtype=np.uint8) * 255
        transform_info = None
    
    # 步骤2: 多算法融合检测
    candidates = _multi_method_detection(paper_region, paper_mask)
    
    # 步骤3: 智能候选点筛选
    best_candidate = _intelligent_candidate_selection(paper_region, candidates)
    
    # 步骤4: 坐标转换回原图
    if best_candidate and transform_info:
        return _transform_to_original(best_candidate, transform_info, image.shape)
    
    return best_candidate

def _detect_and_extract_paper(image):
    """检测并提取白色方形纸板区域"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 多阈值检测白色区域
    paper_masks = []
    
    # 方法1: 基于全局阈值
    mean_val = np.mean(gray)
    _, mask1 = cv2.threshold(gray, min(200, mean_val + 40), 255, cv2.THRESH_BINARY)
    paper_masks.append(mask1)
    
    # 方法2: 基于OTSU阈值
    _, mask2 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    paper_masks.append(mask2)
    
    # 方法3: 自适应阈值
    mask3 = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                  cv2.THRESH_BINARY, 21, 10)
    paper_masks.append(mask3)
    
    # 融合多个掩码
    combined_mask = np.zeros_like(gray)
    for mask in paper_masks:
        combined_mask = cv2.bitwise_or(combined_mask, mask)
    
    # 形态学操作清理
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
    
    # 查找最大的四边形轮廓
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    best_quad = None
    max_area = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 5000:  # 纸板应该足够大
            continue
        
        # 多边形逼近
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 检查是否为四边形
        if len(approx) == 4 and area > max_area:
            max_area = area
            best_quad = approx
    
    if best_quad is None:
        return None, None, None
    
    # 透视变换提取纸板
    paper_region, transform_info = _perspective_transform(image, best_quad)
    paper_mask = np.ones(paper_region.shape[:2], dtype=np.uint8) * 255
    
    return paper_region, paper_mask, transform_info

def _perspective_transform(image, quad_points):
    """透视变换提取纸板区域"""
    # 排序四个角点
    points = quad_points.reshape(4, 2).astype(np.float32)
    rect = _order_quad_points(points)
    
    # 计算目标尺寸
    width_a = np.linalg.norm(rect[2] - rect[3])
    width_b = np.linalg.norm(rect[1] - rect[0])
    max_width = max(int(width_a), int(width_b))
    
    height_a = np.linalg.norm(rect[1] - rect[2])
    height_b = np.linalg.norm(rect[0] - rect[3])
    max_height = max(int(height_a), int(height_b))
    
    # 目标点
    dst = np.array([
        [0, 0],
        [max_width - 1, 0],
        [max_width - 1, max_height - 1],
        [0, max_height - 1]
    ], dtype=np.float32)
    
    # 计算变换矩阵
    M = cv2.getPerspectiveTransform(rect, dst)
    
    # 应用变换
    warped = cv2.warpPerspective(image, M, (max_width, max_height))
    
    return warped, {'matrix': M, 'original_points': rect, 'target_points': dst}

def _order_quad_points(pts):
    """排序四边形的四个角点"""
    rect = np.zeros((4, 2), dtype=np.float32)
    
    # 计算点的和与差
    s = pts.sum(axis=1)
    diff = np.diff(pts, axis=1)
    
    # 左上角：和最小
    rect[0] = pts[np.argmin(s)]
    # 右下角：和最大
    rect[2] = pts[np.argmax(s)]
    # 右上角：差最小
    rect[1] = pts[np.argmin(diff)]
    # 左下角：差最大
    rect[3] = pts[np.argmax(diff)]
    
    return rect

def _multi_method_detection(image, mask):
    """多方法融合检测激光点候选"""
    candidates = []
    
    # 方法1: 增强亮度检测
    candidates.extend(_enhanced_brightness_detection(image, mask))
    
    # 方法2: 多尺度圆形检测
    candidates.extend(_multi_scale_circle_detection(image, mask))
    
    # 方法3: 梯度峰值检测
    candidates.extend(_gradient_peak_detection(image, mask))
    
    # 方法4: 颜色空间融合
    candidates.extend(_color_fusion_detection(image, mask))
    
    # 方法5: 模板匹配检测
    candidates.extend(_template_matching_detection(image, mask))
    
    return candidates

def _enhanced_brightness_detection(image, mask):
    """增强的亮度检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 多阈值亮度检测
    max_val = np.max(gray[mask > 0]) if np.any(mask > 0) else np.max(gray)
    
    for threshold_offset in [5, 10, 15, 20]:
        threshold = max(200, max_val - threshold_offset)
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        binary = cv2.bitwise_and(binary, mask)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 2 <= area <= 150:
                # 计算质心
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 计算特征得分
                    score = _calculate_brightness_score(gray, (cx, cy), area)
                    candidates.append({
                        'point': (cx, cy),
                        'score': score,
                        'method': 'brightness',
                        'area': area
                    })
    
    return candidates

def _calculate_brightness_score(gray, point, area):
    """计算亮度特征得分"""
    x, y = point
    h, w = gray.shape
    
    if x < 3 or y < 3 or x >= w-3 or y >= h-3:
        return 0
    
    # 中心亮度
    center_val = gray[y, x]
    
    # 周围亮度
    surrounding = gray[y-3:y+4, x-3:x+4]
    avg_surrounding = np.mean(surrounding)
    
    # 亮度对比度
    contrast = center_val - avg_surrounding
    
    # 面积得分
    area_score = min(area / 20.0, 1.0)
    
    return contrast / 255.0 * 0.7 + area_score * 0.3

def _multi_scale_circle_detection(image, mask):
    """多尺度圆形检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 应用掩码
    masked_gray = cv2.bitwise_and(gray, mask)
    
    # 多参数霍夫圆检测
    for dp in [1, 2]:
        for min_dist in [10, 15, 20]:
            for param1 in [50, 100]:
                for param2 in [15, 20, 25]:
                    circles = cv2.HoughCircles(
                        masked_gray,
                        cv2.HOUGH_GRADIENT,
                        dp=dp,
                        minDist=min_dist,
                        param1=param1,
                        param2=param2,
                        minRadius=2,
                        maxRadius=15
                    )
                    
                    if circles is not None:
                        circles = np.round(circles[0, :]).astype("int")
                        for (x, y, r) in circles:
                            if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                                score = _calculate_circle_score(gray, (x, y), r)
                                candidates.append({
                                    'point': (x, y),
                                    'score': score,
                                    'method': 'circle',
                                    'radius': r
                                })
    
    return candidates

def _calculate_circle_score(gray, center, radius):
    """计算圆形检测得分"""
    x, y = center
    h, w = gray.shape
    
    if x < radius or y < radius or x >= w-radius or y >= h-radius:
        return 0
    
    # 中心亮度
    center_val = gray[y, x]
    
    # 圆周亮度
    angles = np.linspace(0, 2*np.pi, 16, endpoint=False)
    circle_vals = []
    for angle in angles:
        px = int(x + radius * np.cos(angle))
        py = int(y + radius * np.sin(angle))
        if 0 <= px < w and 0 <= py < h:
            circle_vals.append(gray[py, px])
    
    if not circle_vals:
        return 0
    
    avg_circle = np.mean(circle_vals)
    contrast = center_val - avg_circle
    
    return max(0, contrast / 255.0)

def _gradient_peak_detection(image, mask):
    """梯度峰值检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []

    # 计算梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    magnitude = np.sqrt(grad_x**2 + grad_y**2)

    # 应用掩码
    magnitude = cv2.bitwise_and(magnitude.astype(np.uint8), mask)

    # 查找局部最大值
    kernel = np.ones((5, 5))
    local_max = cv2.dilate(magnitude, kernel) == magnitude

    # 阈值筛选
    threshold = np.percentile(magnitude[mask > 0], 90) if np.any(mask > 0) else 0
    peaks = local_max & (magnitude > threshold)

    # 提取候选点
    y_coords, x_coords = np.where(peaks)
    for x, y in zip(x_coords, y_coords):
        score = magnitude[y, x] / 255.0
        candidates.append({
            'point': (x, y),
            'score': score,
            'method': 'gradient'
        })

    return candidates

def _color_fusion_detection(image, mask):
    """颜色空间融合检测"""
    candidates = []

    # HSV检测
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

    # 多红色范围检测
    red_ranges = [
        ([0, 50, 50], [10, 255, 255]),
        ([170, 50, 50], [180, 255, 255]),
        ([0, 80, 120], [15, 255, 255]),
        ([165, 80, 120], [180, 255, 255])
    ]

    combined_red_mask = np.zeros(image.shape[:2], dtype=np.uint8)

    for lower, upper in red_ranges:
        red_mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
        combined_red_mask = cv2.bitwise_or(combined_red_mask, red_mask)

    # 应用纸板掩码
    combined_red_mask = cv2.bitwise_and(combined_red_mask, mask)

    # LAB颜色空间检测
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)

    # A通道增强红色
    a_enhanced = cv2.normalize(a, None, 0, 255, cv2.NORM_MINMAX)
    _, a_mask = cv2.threshold(a_enhanced, 130, 255, cv2.THRESH_BINARY)
    a_mask = cv2.bitwise_and(a_mask, mask)

    # 融合颜色掩码
    color_mask = cv2.bitwise_or(combined_red_mask, a_mask)

    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_OPEN, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(color_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        area = cv2.contourArea(contour)
        if 3 <= area <= 200:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                candidates.append({
                    'point': (cx, cy),
                    'score': area / 100.0,
                    'method': 'color',
                    'area': area
                })

    return candidates

def _template_matching_detection(image, mask):
    """模板匹配检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []

    # 创建激光点模板
    templates = _create_laser_templates()

    for template_name, template in templates.items():
        # 多尺度匹配
        for scale in [0.7, 1.0, 1.3]:
            scaled_template = cv2.resize(template, None, fx=scale, fy=scale)

            if (scaled_template.shape[0] >= gray.shape[0] or
                scaled_template.shape[1] >= gray.shape[1]):
                continue

            # 模板匹配
            result = cv2.matchTemplate(gray, scaled_template, cv2.TM_CCOEFF_NORMED)

            # 查找高分匹配
            locations = np.where(result >= 0.6)
            for pt in zip(*locations[::-1]):
                x = pt[0] + scaled_template.shape[1] // 2
                y = pt[1] + scaled_template.shape[0] // 2

                # 检查是否在掩码区域内
                if (0 <= x < mask.shape[1] and 0 <= y < mask.shape[0] and
                    mask[y, x] > 0):
                    score = result[pt[1], pt[0]]
                    candidates.append({
                        'point': (x, y),
                        'score': score,
                        'method': 'template',
                        'template': template_name,
                        'scale': scale
                    })

    return candidates

def _create_laser_templates():
    """创建激光点模板"""
    templates = {}

    # 圆形模板
    for radius in [3, 5, 7]:
        size = radius * 4
        template = np.zeros((size, size), dtype=np.uint8)
        cv2.circle(template, (size//2, size//2), radius, 255, -1)
        # 高斯模糊模拟光晕
        template = cv2.GaussianBlur(template, (radius//2*2+1, radius//2*2+1), radius/3)
        templates[f'circle_{radius}'] = template

    # 高斯斑点模板
    for size in [7, 11, 15]:
        template = np.zeros((size, size), dtype=np.float32)
        center = size // 2
        sigma = size / 6

        for i in range(size):
            for j in range(size):
                dist_sq = (i - center)**2 + (j - center)**2
                template[i, j] = np.exp(-dist_sq / (2 * sigma**2))

        template = (template * 255).astype(np.uint8)
        templates[f'gaussian_{size}'] = template

    return templates

def _intelligent_candidate_selection(image, candidates):
    """智能候选点选择"""
    if not candidates:
        return None

    if len(candidates) == 1:
        return candidates[0]['point']

    # 聚类相近的候选点
    clustered_candidates = _cluster_candidates(candidates)

    # 为每个聚类计算综合得分
    best_cluster = None
    best_score = 0

    for cluster in clustered_candidates:
        score = _evaluate_cluster(image, cluster)
        if score > best_score:
            best_score = score
            best_cluster = cluster

    if best_cluster:
        # 返回聚类中心
        points = np.array([c['point'] for c in best_cluster])
        center_x = int(np.mean(points[:, 0]))
        center_y = int(np.mean(points[:, 1]))
        return (center_x, center_y)

    return None

def _cluster_candidates(candidates, distance_threshold=15):
    """聚类候选点"""
    if not candidates:
        return []

    points = np.array([c['point'] for c in candidates])
    clusters = []
    used = [False] * len(candidates)

    for i, point in enumerate(points):
        if used[i]:
            continue

        # 创建新聚类
        cluster = [candidates[i]]
        used[i] = True

        # 查找相近的点
        for j, other_point in enumerate(points):
            if used[j]:
                continue

            distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
            if distance <= distance_threshold:
                cluster.append(candidates[j])
                used[j] = True

        clusters.append(cluster)

    return clusters

def _evaluate_cluster(image, cluster):
    """评估聚类质量"""
    if not cluster:
        return 0

    # 基础得分
    num_candidates = len(cluster)
    avg_score = np.mean([c['score'] for c in cluster])

    # 方法多样性
    methods = set(c['method'] for c in cluster)
    diversity_bonus = len(methods) * 0.1

    # 位置一致性
    points = np.array([c['point'] for c in cluster])
    if len(points) > 1:
        center = np.mean(points, axis=0)
        distances = np.linalg.norm(points - center, axis=1)
        consistency = 1.0 / (1.0 + np.std(distances))
    else:
        consistency = 1.0

    # 图像特征验证
    center_point = tuple(np.mean(points, axis=0).astype(int))
    feature_score = _verify_laser_features(image, center_point)

    # 综合得分
    total_score = (avg_score * 0.4 +
                  diversity_bonus +
                  consistency * 0.2 +
                  feature_score * 0.3 +
                  min(num_candidates / 5, 0.1))

    return total_score

def _verify_laser_features(image, point):
    """验证激光点特征"""
    x, y = point
    h, w = image.shape[:2]

    if x < 5 or y < 5 or x >= w-5 or y >= h-5:
        return 0

    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 局部亮度特征
    center_val = gray[y, x]
    local_region = gray[y-5:y+6, x-5:x+6]
    avg_local = np.mean(local_region)
    brightness_score = (center_val - avg_local) / 255.0

    # 圆形度检查
    radius = 3
    angles = np.linspace(0, 2*np.pi, 8, endpoint=False)
    circle_vals = []

    for angle in angles:
        px = int(x + radius * np.cos(angle))
        py = int(y + radius * np.sin(angle))
        if 0 <= px < w and 0 <= py < h:
            circle_vals.append(gray[py, px])

    if circle_vals:
        circle_std = np.std(circle_vals)
        circularity_score = 1.0 / (1.0 + circle_std / 50.0)
    else:
        circularity_score = 0

    return max(0, min(1, brightness_score * 0.6 + circularity_score * 0.4))

def _transform_to_original(point, transform_info, original_shape):
    """将点坐标转换回原图坐标系"""
    if not transform_info:
        return point

    x, y = point

    # 计算逆变换矩阵
    inv_matrix = cv2.invert(transform_info['matrix'])[1]

    # 转换点坐标
    point_array = np.array([[[x, y]]], dtype=np.float32)
    transformed = cv2.perspectiveTransform(point_array, inv_matrix)

    new_x, new_y = transformed[0][0]

    # 确保坐标在图像范围内
    h, w = original_shape[:2]
    new_x = max(0, min(w-1, int(new_x)))
    new_y = max(0, min(h-1, int(new_y)))

    return (new_x, new_y)

def test_enhanced_detector():
    """测试增强检测器"""
    cap = cv2.VideoCapture('output.mp4')

    if not cap.isOpened():
        print("无法打开视频文件")
        return

    frame_count = 0
    detection_count = 0
    total_time = 0

    print("开始增强型激光点检测测试...")
    print("目标: 20 FPS @ 640x480 分辨率")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1

        # 调整到目标分辨率
        frame = cv2.resize(frame, (640, 480))

        start_time = time.time()

        # 增强检测
        laser_point = detect_laser_point_enhanced(frame)

        end_time = time.time()
        total_time += (end_time - start_time)

        if laser_point:
            detection_count += 1
            x, y = laser_point

            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y})")

            # 绘制检测结果
            cv2.circle(frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(frame, f"({x}, {y})", (x+15, y-15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 显示FPS
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

            # 显示性能状态
            status = "OK" if current_fps >= 20 else "LOW"
            color = (0, 255, 0) if current_fps >= 20 else (0, 0, 255)
            cv2.putText(frame, f"Status: {status}", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

        cv2.imshow('Enhanced Laser Detection', frame)

        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s') and laser_point:
            filename = f"enhanced_detection_{frame_count:04d}.jpg"
            cv2.imwrite(filename, frame)
            print(f"保存图像: {filename}")

    cap.release()
    cv2.destroyAllWindows()

    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0

    print(f"\n=== 增强检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")
    print(f"目标性能: 20 FPS @ 640x480")
    print(f"性能达标: {'✓' if avg_fps >= 20 else '✗'}")

if __name__ == "__main__":
    print("增强型激光点检测器")
    print("专注白色方形纸板区域，多算法融合")
    print("目标: 20 FPS @ 640x480 分辨率")
    test_enhanced_detector()

def _gradient_peak_detection(image, mask):
    """梯度峰值检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 计算梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    magnitude = np.sqrt(grad_x**2 + grad_y**2)
    
    # 应用掩码
    magnitude = cv2.bitwise_and(magnitude.astype(np.uint8), mask)
    
    # 查找局部最大值
    kernel = np.ones((5, 5))
    local_max = cv2.dilate(magnitude, kernel) == magnitude
    
    # 阈值筛选
    threshold = np.percentile(magnitude[mask > 0], 90) if np.any(mask > 0) else 0
    peaks = local_max & (magnitude > threshold)
    
    # 提取候选点
    y_coords, x_coords = np.where(peaks)
    for x, y in zip(x_coords, y_coords):
        score = magnitude[y, x] / 255.0
        candidates.append({
            'point': (x, y),
            'score': score,
            'method': 'gradient'
        })
    
    return candidates
