# 250713

import cv2
import numpy as np

def img_show(img, name='xixi'):
    cv2.imshow(name, img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

img = cv2.imread('bilicv/img06.png')
# print('img.shape:', img.shape)
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

img_show(gray)

dst = cv2.corner<PERSON><PERSON><PERSON>(gray, 2, 3, 0.04)
# print('dst.shape:', dst.shape)

img[dst > 0.02 * dst.max()] = [0, 0, 255]
img_show(img, 'img')