{"cells": [{"cell_type": "markdown", "id": "87e2137c", "metadata": {}, "source": ["# 第13章 实例分割"]}, {"cell_type": "markdown", "id": "451fdd2a", "metadata": {}, "source": ["## 13.1 简介"]}, {"cell_type": "markdown", "id": "bd295990", "metadata": {}, "source": ["图像实例分割(Instance Segmentation)是在语义检测(Semantic Segmentation)的基础上进一步细化，分离对象的前景与背景，实现像素级别的对象分离。并且图像的语义分割与图像的实例分割是两个不同的概念，语义分割仅仅会区别分割出不同类别的物体，而实例分割则会进一步的分割出同一个类中的不同实例的物体。如图 13-1 所示，相比语义分割，实例分割要求我们在图像中捕捉到每一只羊，而不是简简单单的给像素分类。\n", "\n", "\n", "<center>\n", "    <img style=\"border-radius: 0.3125em;\" \n", "    src=\"https://pic4.zhimg.com/80/v2-f021bd0866f443ef25ea16020570044b_1440w.jpg\n", "\" width=600>\n", "    <br>\n", "    <div style=\"color:orange; \n", "    display: inline-block;\n", "    color: #999;\n", "    padding: 2px;\">图13-1 实例分割与语义分割的关系。</div>\n", "</center>\n", "\n", "在这一章中，我们将学习实例分割的相关知识，并动手实现实例分割。\n"]}, {"cell_type": "markdown", "id": "7847b2d1", "metadata": {}, "source": ["## 13.2 数据集与评测指标"]}, {"cell_type": "markdown", "id": "c8a3aa11", "metadata": {}, "source": ["我们使用MS COCO作为实例分割的数据集，并使用之前介绍过的IoU作为评测指标。"]}, {"cell_type": "markdown", "id": "a843a257", "metadata": {}, "source": ["## 13.3 Mask R-CNN\n"]}, {"cell_type": "markdown", "id": "b03c7917", "metadata": {}, "source": ["我们重点介绍Mask R-CNN作为实例分割的代表模型。Mask R-CNN由Facebook的何凯明等人在2017年的ICCV上提出，是一种在有效检测目标的同时输出高质量分割的模型。Mask R-CNN不仅能够同时进行目标检测与分割，还能很容易地扩展到其他任务，比如人体关键点检测与人体姿势识别，并能取得很好的效果。\n", "\n", "Mask R-CNN的结构如图 13-2 所示。Mask R-CNN拓展自Faster R-CNN，我们可以通过图 13-2 发现，Mask R-CNN与Faster R-CNN相比有两个明显区别：\n", "\n", "1. Mask R-CNN提出了RoI Align取代Faster R-CNN中的RoI Pooling层；\n", "2. Mask R-CNN提出了一个用于预测掩码（mask）的分支。\n", "\n", "\n", "\n", "<center>\n", "    <img style=\"border-radius: 0.3125em;\" \n", "    src=\"https://pic4.zhimg.com/80/v2-38ff78aa37a735b889c5201151340eef_1440w.jpg\n", "\" width=600>\n", "    <br>\n", "    <div style=\"color:orange; \n", "    display: inline-block;\n", "    color: #999;\n", "    padding: 2px;\">图13-2 Mask R-CNN结构图。</div>\n", "</center>\n", "\n", "\n", "\n", "在RoI Pooling层中，为了得到固定空间大小的特征图，我们需要对特征点的坐标做两次量化操作：第一次是从图像坐标映射到特征图坐标，第二次是从特征图坐标映射到RoI特征坐标。具体而言，在上述过程中，特征点的坐标有可能是一个浮点数，而两次量化引入的误差会导致图像中的像素与特征中像素的偏差。这一误差会极大的影响整个检测算法的性能，因此是一个严重的问题。为了解决这个问题，在RoI Align中我们不适用量化来对浮点数的坐标进行处理，而是直接使用这些浮点数。那么我们该如何处理浮点数的坐标呢？回想之前学习过的双线性插值，对于图像中的任意一个位置，我们都可以利用双线性插值的方式来得到其对应的像素值。这一过程中没有用到量化操作，没有引入误差，即原图中的像素和特征图中的像素是完全对齐的，没有偏差，这不仅会提高检测的精度，同时也会有利于实例分割。\n", "\n", "在利用RoI Align层得到了固定空间大小的特征图之后，我们便可以利用这些特征图进行后续的任务。同Faster R-CNN一样，Mask R-CNN利用全连接层对这些特征图进行类别的预测以及候选框的判定。除此之外，Mask R-CNN还提出了一条专门用于预测掩码的分支，如图 13-2 所示。在这一分支中，特征图首先会经过一个“头部”结构，这会提升特征图的维度，使之后预测掩码更加准确。\n", "\n", "\n", "废话不多说，咱们看代码！\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "0947d686", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ca5a1215", "metadata": {}, "source": ["## 13.4 总结"]}, {"cell_type": "markdown", "id": "4b0f3da8", "metadata": {}, "source": ["在这一章中，我们学习了实例分割的基础知识，并动手实现了一个实例分割的代表性模型——Mask R-CNN。在之后的章节中，我们将继续探索计算机视觉的另一个领域——人体姿势估计。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 5}