import time
import cv2
import numpy as np

pre_time = time.perf_counter_ns()

# 初始化折线图相关变量
fps_history = []
timestamps = []
graph_width = 800
graph_height = 300
graph_margin = 50  # 图像边缘留白
max_history = 1000  # 最多保存的FPS数量
line_color = (255, 255, 0)  # 绿色折线

# 创建折线图画布（黑色背景）
graph_image = np.zeros((graph_height, graph_width, 3), dtype=np.uint8)

vc = cv2.VideoCapture(1)

vc.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减小缓冲区
# vc.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M','J','P','G')) # 使用MJPEG

vc.set(cv2.CAP_PROP_FRAME_WIDTH, 2560)
vc.set(cv2.CAP_PROP_FRAME_HEIGHT, 1440)

vc.set(cv2.CAP_PROP_FPS, 30)

print(vc.get(cv2.CAP_PROP_FPS))

# vc.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 0.25表示手动曝光模式
# exposure_time = 10  # 示例值（30ms），实际值需根据摄像头调整
# vc.set(cv2.CAP_PROP_EXPOSURE, exposure_time / 1000)  # OpenCV需要秒为单位

# 检查打开是否正确
if vc.isOpened():
    open, frame = vc.read()
else:
    open = False
    print('Failed to open camera')

# 创建两个窗口
cv2.namedWindow('Video Feed', cv2.WINDOW_NORMAL)
cv2.namedWindow('FPS Monitor', cv2.WINDOW_NORMAL)
cv2.resizeWindow('FPS Monitor', graph_width, graph_height)

while open:
    ret, frame = vc.read()
    real_time = time.perf_counter_ns()
    if frame is None:
        break
    if ret is True:
        # 计算当前FPS
        current_fps = 1000000000 / (real_time - pre_time)
        
        # 更新FPS历史数据
        fps_history.append(current_fps)
        timestamps.append(time.perf_counter_ns())
        
        # 移除10秒前的旧数据
        while fps_history and (timestamps[-1] - timestamps[0]) > 10000000000:  # 10秒
            fps_history.pop(0)
            timestamps.pop(0)
        
        # 在视频上显示当前FPS
        cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.65, (0, 255, 0), 2)
        cv2.imshow('Video Feed', frame[250:1200, 1035:1915, ])
        
        # 准备FPS折线图
        graph_image.fill(0)  # 清空画布
        
        if len(fps_history) > 1:
            # 计算纵轴范围（FPS范围）
            min_fps = max(0, min(fps_history) * 0.9)
            max_fps = max(fps_history) * 1.1
            
            # 绘制坐标轴
            cv2.line(graph_image, 
                    (graph_margin, graph_margin), 
                    (graph_margin, graph_height - graph_margin), 
                    (128, 128, 128), 1)
            cv2.line(graph_image, 
                    (graph_margin, graph_height - graph_margin), 
                    (graph_width - graph_margin, graph_height - graph_margin), 
                    (128, 128, 128), 1)
            
            # 绘制参考线
            for i in range(0, int(max_fps) + 20, 10):
                y_pos = graph_height - graph_margin - int(
                    (i - min_fps) / (max_fps - min_fps) * (graph_height - 2 * graph_margin))
                if y_pos > graph_margin:
                    cv2.line(graph_image, 
                            (graph_margin, y_pos), 
                            (graph_width - graph_margin, y_pos), 
                            (50, 50, 50), 1)
                    cv2.putText(graph_image, str(i), 
                               (5, y_pos + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, 
                               (200, 200, 200), 1)
            
            # 绘制折线
            points = []
            for i, fps in enumerate(fps_history):
                # 计算点坐标
                x = int(graph_margin + i * (graph_width - 2 * graph_margin) / len(fps_history))
                y = graph_height - graph_margin - int(
                    (fps - min_fps) / (max_fps - min_fps) * (graph_height - 2 * graph_margin))
                points.append((x, y))
            
            # 连接所有点形成折线
            for i in range(1, len(points)):
                cv2.line(graph_image, points[i-1], points[i], line_color, 2)
            
            # 显示平均FPS
            avg_fps = sum(fps_history) / len(fps_history)
            cv2.putText(graph_image, f"Avg FPS: {avg_fps:.1f}", 
                       (graph_width - 200, 25), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (0, 255, 255), 1)
        
        # 显示当前FPS
        cv2.putText(graph_image, f"Current FPS: {current_fps:.1f}", 
                   (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        # 显示折线图
        cv2.imshow('FPS Monitor', graph_image)
        
        # 更新时间戳
        pre_time = time.perf_counter_ns()
        
        # 检查退出条件
        if cv2.waitKey(1) & 0xFF == 27:  # ESC键退出
            break

# 释放资源
vc.release()
cv2.destroyAllWindows()
