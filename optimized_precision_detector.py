#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化的高精度激光点检测器
专注白色方形纸板区域，平衡精度与性能
目标: 20FPS@640x480，6Tops INT8算力
"""

import cv2
import numpy as np
import time

def detect_laser_point_optimized(image):
    """
    性能优化的高精度激光点检测
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 步骤1: 快速ROI检测（简化版）
    roi_region, roi_offset = _fast_roi_detection(image)
    
    # 步骤2: 在ROI内进行精确检测
    candidates = []
    
    # 算法1: 优化的亮度检测（主要方法）
    bright_candidates = _optimized_brightness_detection(roi_region)
    candidates.extend(bright_candidates)
    
    # 算法2: 快速红色检测（辅助方法）
    if len(candidates) < 3:  # 只有在候选点不足时才使用
        red_candidates = _fast_red_detection(roi_region)
        candidates.extend(red_candidates)
    
    # 算法3: 快速圆形检测（备用方法）
    if len(candidates) < 2:  # 只有在候选点很少时才使用
        circle_candidates = _fast_circle_detection(roi_region)
        candidates.extend(circle_candidates)
    
    # 步骤3: 快速候选点选择
    best_point = _fast_candidate_selection(roi_region, candidates)
    
    # 步骤4: 转换回原图坐标
    if best_point and roi_offset:
        return (best_point[0] + roi_offset[0], best_point[1] + roi_offset[1])
    
    return best_point

def _fast_roi_detection(image):
    """快速ROI检测 - 简化版"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    h, w = gray.shape
    
    # 快速阈值检测白色区域
    mean_val = np.mean(gray)
    threshold = min(180, mean_val + 30)
    _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
    
    # 简单形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (8, 8))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    
    # 查找最大连通区域
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return image, (0, 0)
    
    # 找到最大轮廓的边界框
    largest_contour = max(contours, key=cv2.contourArea)
    area = cv2.contourArea(largest_contour)
    
    if area < 3000:  # 如果纸板区域太小，使用全图
        return image, (0, 0)
    
    # 获取边界框并扩展一些边距
    x, y, roi_w, roi_h = cv2.boundingRect(largest_contour)
    
    # 扩展边界框
    margin = 20
    x = max(0, x - margin)
    y = max(0, y - margin)
    roi_w = min(w - x, roi_w + 2 * margin)
    roi_h = min(h - y, roi_h + 2 * margin)
    
    # 提取ROI
    roi = image[y:y+roi_h, x:x+roi_w]
    
    return roi, (x, y)

def _optimized_brightness_detection(image):
    """优化的亮度检测 - 增强版"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []

    # 计算图像统计信息
    mean_brightness = np.mean(gray)
    max_val = np.max(gray)

    # 适中的亮度检查
    if max_val < 220 or (max_val - mean_brightness) < 30:
        return candidates

    # 使用适中的阈值
    for threshold in [max_val - 8, max_val - 18]:
        if threshold < 210:
            continue

        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)

        # 轻量级形态学操作
        kernel = np.ones((3, 3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        for contour in contours:
            area = cv2.contourArea(contour)
            if 2 <= area <= 60:  # 更严格的面积限制
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])

                    # 验证激光点特征
                    if _verify_optimized_laser_features(gray, (cx, cy)):
                        brightness_score = gray[cy, cx] / 255.0
                        area_score = min(area / 25.0, 1.0)

                        # 计算圆形度
                        perimeter = cv2.arcLength(contour, True)
                        if perimeter > 0:
                            circularity = 4 * np.pi * area / (perimeter ** 2)
                            circularity_score = min(circularity, 1.0)
                        else:
                            circularity_score = 0

                        score = (brightness_score * 0.6 +
                                area_score * 0.2 +
                                circularity_score * 0.2)

                        # 只保留较高质量候选点
                        if score > 0.5:
                            candidates.append({
                                'point': (cx, cy),
                                'score': score,
                                'method': 'brightness'
                            })

    return candidates

def _verify_optimized_laser_features(gray, point):
    """验证激光点特征 - 优化版"""
    x, y = point
    h, w = gray.shape

    # 边界检查
    if x < 3 or y < 3 or x >= w-3 or y >= h-3:
        return False

    # 中心亮度检查
    center_val = gray[y, x]
    if center_val < 220:  # 激光点应该比较亮
        return False

    # 局部对比度检查
    local_region = gray[y-2:y+3, x-2:x+3]
    avg_surrounding = np.mean(local_region)
    contrast = center_val - avg_surrounding

    if contrast < 20:  # 对比度不够
        return False

    # 检查是否为孤立的亮点
    bright_neighbors = np.sum(local_region > 200)
    if bright_neighbors > 12:  # 如果周围太多亮点，可能是噪声
        return False

    return True

def _fast_red_detection(image):
    """快速红色检测 - 增强版"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []

    # 更严格的红色范围检测
    lower_red1 = np.array([0, 120, 180])
    upper_red1 = np.array([8, 255, 255])
    lower_red2 = np.array([172, 120, 180])
    upper_red2 = np.array([180, 255, 255])

    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)

    # 亮度掩码 - 激光点应该足够亮
    _, brightness_mask = cv2.threshold(gray, 220, 255, cv2.THRESH_BINARY)

    # 融合掩码
    final_mask = cv2.bitwise_and(red_mask, brightness_mask)

    # 轻量级形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        area = cv2.contourArea(contour)
        if 2 <= area <= 80:  # 更严格的面积限制
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])

                # 验证红色激光点特征
                if _verify_red_laser_optimized(image, (cx, cy)):
                    # 计算圆形度
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        circularity = 4 * np.pi * area / (perimeter ** 2)
                        if circularity > 0.5:  # 只保留圆形度较好的
                            score = min(area / 40.0, 1.0) * circularity
                            candidates.append({
                                'point': (cx, cy),
                                'score': score,
                                'method': 'red'
                            })

    return candidates

def _verify_red_laser_optimized(image, point):
    """验证红色激光点特征 - 优化版"""
    x, y = point
    h, w = image.shape[:2]

    # 边界检查
    if x < 2 or y < 2 or x >= w-2 or y >= h-2:
        return False

    # 提取BGR值
    b, g, r = image[y, x]

    # 红色激光点应该红色分量明显高于其他分量
    if r < 180 or r < g + 40 or r < b + 40:
        return False

    return True

def _fast_circle_detection(image):
    """快速圆形检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 只使用一组参数进行霍夫圆检测
    circles = cv2.HoughCircles(
        gray, cv2.HOUGH_GRADIENT,
        dp=1, minDist=15,
        param1=50, param2=20,
        minRadius=2, maxRadius=12
    )
    
    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")
        for (x, y, radius) in circles[:3]:  # 最多只处理前3个圆
            if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                center_val = gray[y, x]
                if center_val > 230:  # 只保留非常亮的圆心
                    score = center_val / 255.0
                    candidates.append({
                        'point': (x, y),
                        'score': score,
                        'method': 'circle',
                        'radius': radius
                    })
    
    return candidates

def _fast_candidate_selection(image, candidates):
    """快速候选点选择 - 增强版"""
    if not candidates:
        return None

    # 首先过滤低质量候选点
    high_quality_candidates = []
    for candidate in candidates:
        if candidate['score'] > 0.4:  # 保留中等以上得分候选点
            high_quality_candidates.append(candidate)

    if not high_quality_candidates:
        return None

    if len(high_quality_candidates) == 1:
        return high_quality_candidates[0]['point']

    # 如果候选点很多，先按得分排序，只考虑前8个
    if len(high_quality_candidates) > 8:
        high_quality_candidates = sorted(high_quality_candidates, key=lambda x: x['score'], reverse=True)[:8]

    # 简单的聚类和评分
    best_candidate = None
    best_score = 0

    for candidate in high_quality_candidates:
        point = candidate['point']
        score = candidate['score']

        # 检查周围是否有其他候选点（简单的聚类）
        nearby_count = 0
        nearby_score_sum = 0

        for other in high_quality_candidates:
            if other == candidate:
                continue
            other_point = other['point']
            distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
            if distance <= 12:  # 更严格的聚类距离
                nearby_count += 1
                nearby_score_sum += other['score']

        # 方法多样性奖励
        methods_nearby = set([candidate['method']])
        for other in high_quality_candidates:
            other_point = other['point']
            distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
            if distance <= 12:
                methods_nearby.add(other['method'])

        diversity_bonus = len(methods_nearby) * 0.1

        # 综合得分：原始得分 + 聚类奖励 + 多样性奖励
        if nearby_count > 0:
            avg_nearby_score = nearby_score_sum / nearby_count
            cluster_bonus = avg_nearby_score * 0.2
        else:
            cluster_bonus = 0

        total_score = score + cluster_bonus + diversity_bonus

        if total_score > best_score:
            best_score = total_score
            best_candidate = candidate

    return best_candidate['point'] if best_candidate and best_score > 0.5 else None

class LaserTracker:
    """激光点跟踪器 - 提供时间稳定性"""
    
    def __init__(self, history_size=3):
        self.history = []
        self.history_size = history_size
        
    def update(self, detection):
        """更新检测结果"""
        if detection is not None:
            self.history.append(detection)
            if len(self.history) > self.history_size:
                self.history.pop(0)
            
            # 如果有足够的历史记录，返回平均位置
            if len(self.history) >= 2:
                avg_x = sum(p[0] for p in self.history) // len(self.history)
                avg_y = sum(p[1] for p in self.history) // len(self.history)
                return (avg_x, avg_y)
            else:
                return detection
        else:
            # 如果当前帧没有检测到，逐渐减少历史记录
            if self.history:
                self.history.pop(0)
            return self.history[-1] if self.history else None

def test_optimized_detector():
    """测试优化检测器"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    tracker = LaserTracker()
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("开始性能优化的高精度激光点检测测试...")
    print("目标: 20 FPS @ 640x480 分辨率")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 调整到目标分辨率
        frame = cv2.resize(frame, (640, 480))

        # 保存原始帧用于显示
        display_frame = frame.copy()

        # 提取ROI区域
        roi_frame = frame[65:410, 260:480, :]
        roi_offset = (260, 65)  # (x_offset, y_offset)

        start_time = time.time()

        # 优化检测
        laser_point = detect_laser_point_optimized(roi_frame)

        # 使用跟踪器稳定结果
        if laser_point:
            # 转换回原图坐标
            original_point = (laser_point[0] + roi_offset[0], laser_point[1] + roi_offset[1])
            tracked_point = tracker.update(original_point)
        else:
            tracked_point = tracker.update(None)

        end_time = time.time()
        total_time += (end_time - start_time)

        if tracked_point:
            detection_count += 1
            x, y = tracked_point

            if frame_count <= 20 or frame_count % 50 == 0:
                roi_x = laser_point[0] if laser_point else "N/A"
                roi_y = laser_point[1] if laser_point else "N/A"
                print(f"Frame {frame_count}: 激光点 ({x}, {y}) [ROI: ({roi_x}, {roi_y})]")

            # 在原图上绘制检测结果
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(display_frame, f"({x}, {y})", (x+15, y-15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

            # 绘制ROI区域边界
            cv2.rectangle(display_frame, (260, 65), (480, 410), (255, 255, 0), 2)
            cv2.putText(display_frame, "ROI", (265, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 显示FPS和状态信息
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(display_frame, f"FPS: {current_fps:.1f}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

            # 显示性能状态
            status = "OK" if current_fps >= 20 else "LOW"
            color = (0, 255, 0) if current_fps >= 20 else (0, 0, 255)
            cv2.putText(display_frame, f"Status: {status}", (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

            # 显示检测信息
            detection_rate = detection_count / frame_count * 100
            cv2.putText(display_frame, f"Detection: {detection_rate:.1f}%", (10, 90),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)

        # 每3帧显示一次以提高性能
        if frame_count % 3 == 0:
            cv2.imshow('Enhanced Optimized Precision Detection', display_frame)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s') and tracked_point:
                # 保存当前帧
                filename = f"enhanced_optimized_{frame_count:04d}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"保存图像: {filename}")
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 优化检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")
    print(f"目标性能: 20 FPS @ 640x480")
    print(f"性能达标: {'✓' if avg_fps >= 20 else '✗'}")
    
    # 性能分析
    if avg_fps >= 20:
        print("🎉 性能达标！可以在6Tops INT8设备上稳定运行")
    else:
        print(f"⚠️  性能不足，需要进一步优化。当前性能为目标的{avg_fps/20*100:.1f}%")

if __name__ == "__main__":
    print("性能优化的高精度激光点检测器")
    print("专注白色方形纸板区域，平衡精度与性能")
    print("目标: 20 FPS @ 640x480 分辨率")
    test_optimized_detector()
