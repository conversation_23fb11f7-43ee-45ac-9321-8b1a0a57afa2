#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化的高精度激光点检测器
专注白色方形纸板区域，平衡精度与性能
目标: 20FPS@640x480，6Tops INT8算力
"""

import cv2
import numpy as np
import time

def detect_laser_point_optimized(image):
    """
    性能优化的高精度激光点检测
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 步骤1: 快速ROI检测（简化版）
    roi_region, roi_offset = _fast_roi_detection(image)
    
    # 步骤2: 在ROI内进行精确检测
    candidates = []
    
    # 算法1: 优化的亮度检测（主要方法）
    bright_candidates = _optimized_brightness_detection(roi_region)
    candidates.extend(bright_candidates)
    
    # 算法2: 快速红色检测（辅助方法）
    if len(candidates) < 3:  # 只有在候选点不足时才使用
        red_candidates = _fast_red_detection(roi_region)
        candidates.extend(red_candidates)
    
    # 算法3: 快速圆形检测（备用方法）
    if len(candidates) < 2:  # 只有在候选点很少时才使用
        circle_candidates = _fast_circle_detection(roi_region)
        candidates.extend(circle_candidates)
    
    # 步骤3: 快速候选点选择
    best_point = _fast_candidate_selection(roi_region, candidates)
    
    # 步骤4: 转换回原图坐标
    if best_point and roi_offset:
        return (best_point[0] + roi_offset[0], best_point[1] + roi_offset[1])
    
    return best_point

def _fast_roi_detection(image):
    """快速ROI检测 - 简化版"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    h, w = gray.shape
    
    # 快速阈值检测白色区域
    mean_val = np.mean(gray)
    threshold = min(180, mean_val + 30)
    _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
    
    # 简单形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (8, 8))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    
    # 查找最大连通区域
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return image, (0, 0)
    
    # 找到最大轮廓的边界框
    largest_contour = max(contours, key=cv2.contourArea)
    area = cv2.contourArea(largest_contour)
    
    if area < 3000:  # 如果纸板区域太小，使用全图
        return image, (0, 0)
    
    # 获取边界框并扩展一些边距
    x, y, roi_w, roi_h = cv2.boundingRect(largest_contour)
    
    # 扩展边界框
    margin = 20
    x = max(0, x - margin)
    y = max(0, y - margin)
    roi_w = min(w - x, roi_w + 2 * margin)
    roi_h = min(h - y, roi_h + 2 * margin)
    
    # 提取ROI
    roi = image[y:y+roi_h, x:x+roi_w]
    
    return roi, (x, y)

def _optimized_brightness_detection(image):
    """优化的亮度检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 快速找到最亮的区域
    max_val = np.max(gray)
    
    if max_val < 200:  # 如果整体亮度不够，跳过
        return candidates
    
    # 只使用两个阈值进行检测
    for threshold in [max_val - 10, max_val - 20]:
        if threshold < 200:
            continue
            
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        
        # 轻量级形态学操作
        kernel = np.ones((3, 3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 3 <= area <= 100:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 简化的得分计算
                    brightness_score = gray[cy, cx] / 255.0
                    area_score = min(area / 30.0, 1.0)
                    score = brightness_score * 0.8 + area_score * 0.2
                    
                    candidates.append({
                        'point': (cx, cy),
                        'score': score,
                        'method': 'brightness'
                    })
    
    return candidates

def _fast_red_detection(image):
    """快速红色检测"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    candidates = []
    
    # 简化的红色范围检测
    lower_red1 = np.array([0, 60, 60])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([170, 60, 60])
    upper_red2 = np.array([180, 255, 255])
    
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)
    
    # 轻量级形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if 3 <= area <= 150:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                candidates.append({
                    'point': (cx, cy),
                    'score': area / 100.0,
                    'method': 'red'
                })
    
    return candidates

def _fast_circle_detection(image):
    """快速圆形检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 只使用一组参数进行霍夫圆检测
    circles = cv2.HoughCircles(
        gray, cv2.HOUGH_GRADIENT,
        dp=1, minDist=15,
        param1=50, param2=20,
        minRadius=2, maxRadius=12
    )
    
    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")
        for (x, y, r) in circles[:5]:  # 最多只处理前5个圆
            if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                center_val = gray[y, x]
                score = center_val / 255.0
                candidates.append({
                    'point': (x, y),
                    'score': score,
                    'method': 'circle'
                })
    
    return candidates

def _fast_candidate_selection(image, candidates):
    """快速候选点选择"""
    if not candidates:
        return None
    
    if len(candidates) == 1:
        return candidates[0]['point']
    
    # 简化的聚类：只考虑距离
    best_candidate = None
    best_score = 0
    
    # 如果候选点很多，先按得分排序，只考虑前10个
    if len(candidates) > 10:
        candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)[:10]
    
    # 简单的聚类和评分
    for candidate in candidates:
        point = candidate['point']
        score = candidate['score']
        
        # 检查周围是否有其他候选点（简单的聚类）
        nearby_count = 0
        for other in candidates:
            if other == candidate:
                continue
            other_point = other['point']
            distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
            if distance <= 15:
                nearby_count += 1
        
        # 综合得分：原始得分 + 聚类奖励
        total_score = score + nearby_count * 0.1
        
        if total_score > best_score:
            best_score = total_score
            best_candidate = candidate
    
    return best_candidate['point'] if best_candidate else None

class LaserTracker:
    """激光点跟踪器 - 提供时间稳定性"""
    
    def __init__(self, history_size=3):
        self.history = []
        self.history_size = history_size
        
    def update(self, detection):
        """更新检测结果"""
        if detection is not None:
            self.history.append(detection)
            if len(self.history) > self.history_size:
                self.history.pop(0)
            
            # 如果有足够的历史记录，返回平均位置
            if len(self.history) >= 2:
                avg_x = sum(p[0] for p in self.history) // len(self.history)
                avg_y = sum(p[1] for p in self.history) // len(self.history)
                return (avg_x, avg_y)
            else:
                return detection
        else:
            # 如果当前帧没有检测到，逐渐减少历史记录
            if self.history:
                self.history.pop(0)
            return self.history[-1] if self.history else None

def test_optimized_detector():
    """测试优化检测器"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    tracker = LaserTracker()
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("开始性能优化的高精度激光点检测测试...")
    print("目标: 20 FPS @ 640x480 分辨率")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 调整到目标分辨率
        frame = cv2.resize(frame, (640, 480))
        frame = frame[65:410, 260:480, :]
        
        start_time = time.time()
        
        # 优化检测
        laser_point = detect_laser_point_optimized(frame)
        
        # 使用跟踪器稳定结果
        tracked_point = tracker.update(laser_point)
        
        end_time = time.time()
        total_time += (end_time - start_time)
        
        if tracked_point:
            detection_count += 1
            x, y = tracked_point
            
            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y})")
            
            # 绘制检测结果
            cv2.circle(frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # 显示FPS
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # 显示性能状态
            status = "OK" if current_fps >= 20 else "LOW"
            color = (0, 255, 0) if current_fps >= 20 else (0, 0, 255)
            cv2.putText(frame, f"Status: {status}", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # 每5帧显示一次以提高性能
        if frame_count % 5 == 0:
            cv2.imshow('Optimized Precision Detection', frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 优化检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")
    print(f"目标性能: 20 FPS @ 640x480")
    print(f"性能达标: {'✓' if avg_fps >= 20 else '✗'}")
    
    # 性能分析
    if avg_fps >= 20:
        print("🎉 性能达标！可以在6Tops INT8设备上稳定运行")
    else:
        print(f"⚠️  性能不足，需要进一步优化。当前性能为目标的{avg_fps/20*100:.1f}%")

if __name__ == "__main__":
    print("性能优化的高精度激光点检测器")
    print("专注白色方形纸板区域，平衡精度与性能")
    print("目标: 20 FPS @ 640x480 分辨率")
    test_optimized_detector()
