# 250722

# 纯视觉，激光点识别不太行

import time
import cv2
import numpy as np

class RecommendedLaserDetector:
    def __init__(self):
        # 基于分析结果的优化参数
        self.params = {
            'min_brightness': 130,
            'brightness_threshold_offset': 20,
            'min_contrast': 5,
            'min_area': 1,
            'max_area': 180,
            'min_circularity': 0.15,
            'score_threshold': 0.25
        }
        print("推荐检测器已初始化")
    
    def detect_laser_point(self, image):
        """检测激光点 - 平衡检测率和精度"""
        if image is None:
            return None
        
        candidates = []
        
        # 主要方法：多阈值亮度检测
        bright_candidates = self._multi_threshold_detection(image)
        candidates.extend(bright_candidates)
        
        # 辅助方法：红色检测
        if len(candidates) < 3:
            red_candidates = self._red_detection(image)
            candidates.extend(red_candidates)
        
        # 备用方法：形态学检测
        if len(candidates) < 2:
            morph_candidates = self._morphological_detection(image)
            candidates.extend(morph_candidates)
        
        # 智能选择
        return self._intelligent_selection(candidates)
    
    def _multi_threshold_detection(self, image):
        """多阈值亮度检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        max_val = np.max(gray)
        mean_val = np.mean(gray)
        
        # 使用多个阈值策略
        thresholds = [
            max_val - 15,  # 最亮区域
            max_val - 25,  # 次亮区域
            max_val - 35,  # 较亮区域
            mean_val + 40, # 高于平均值
            mean_val + 25  # 中等亮度
        ]
        
        for i, threshold in enumerate(thresholds):
            if threshold < self.params['min_brightness']:
                continue
            
            _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
            
            # 轻微形态学操作
            if i < 2:  # 对最亮的区域使用更严格的形态学操作
                kernel = np.ones((3, 3), np.uint8)
                binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.params['min_area'] <= area <= self.params['max_area']:
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        if self._verify_point(gray, (cx, cy)):
                            # 计算综合得分
                            brightness_score = gray[cy, cx] / 255.0
                            area_score = min(area / 50.0, 1.0)
                            
                            # 圆形度
                            perimeter = cv2.arcLength(contour, True)
                            if perimeter > 0:
                                circularity = 4 * np.pi * area / (perimeter ** 2)
                            else:
                                circularity = 0
                            
                            # 阈值权重（越严格的阈值权重越高）
                            threshold_weight = 1.0 - i * 0.1
                            
                            score = (brightness_score * 0.4 + 
                                   area_score * 0.2 + 
                                   circularity * 0.2 + 
                                   threshold_weight * 0.2)
                            
                            if score >= self.params['score_threshold']:
                                candidates.append({
                                    'point': (cx, cy),
                                    'score': score,
                                    'method': 'brightness',
                                    'threshold_level': i
                                })
        
        return candidates
    
    def _red_detection(self, image):
        """红色检测"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 红色范围
        red_ranges = [
            ([0, 50, 100], [12, 255, 255]),
            ([168, 50, 100], [180, 255, 255])
        ]
        
        combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)
        
        for lower, upper in red_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # 亮度掩码
        _, brightness_mask = cv2.threshold(gray, self.params['min_brightness'], 255, cv2.THRESH_BINARY)
        
        # 融合掩码
        final_mask = cv2.bitwise_and(combined_mask, brightness_mask)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.params['min_area'] <= area <= self.params['max_area']:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    score = min(area / 60.0, 1.0) * 0.8  # 红色检测得分稍低
                    candidates.append({
                        'point': (cx, cy),
                        'score': score,
                        'method': 'red'
                    })
        
        return candidates
    
    def _morphological_detection(self, image):
        """形态学检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 顶帽变换检测小的亮点
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        tophat = cv2.morphologyEx(gray, cv2.MORPH_TOPHAT, kernel)
        
        # 阈值化
        _, binary = cv2.threshold(tophat, 20, 255, cv2.THRESH_BINARY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 3 <= area <= 100:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    if gray[cy, cx] > self.params['min_brightness']:
                        score = gray[cy, cx] / 255.0 * 0.6  # 形态学检测得分较低
                        candidates.append({
                            'point': (cx, cy),
                            'score': score,
                            'method': 'morphology'
                        })
        
        return candidates
    
    def _verify_point(self, gray, point):
        """验证点的特征"""
        x, y = point
        h, w = gray.shape
        
        if x < 4 or y < 4 or x >= w-4 or y >= h-4:
            return False
        
        center_val = gray[y, x]
        if center_val < self.params['min_brightness']:
            return False
        
        # 对比度检查
        local_region = gray[y-3:y+4, x-3:x+4]
        avg_surrounding = np.mean(local_region)
        contrast = center_val - avg_surrounding
        
        if contrast < self.params['min_contrast']:
            return False
        
        return True
    
    def _intelligent_selection(self, candidates):
        """智能选择最佳候选点"""
        if not candidates:
            return None
        
        if len(candidates) == 1:
            return candidates[0]['point']
        
        # 按得分排序
        candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)
        
        # 聚类分析
        clusters = self._cluster_candidates(candidates)
        
        # 评估聚类
        best_cluster = None
        best_score = 0
        
        for cluster in clusters:
            cluster_score = self._evaluate_cluster(cluster)
            if cluster_score > best_score:
                best_score = cluster_score
                best_cluster = cluster
        
        if best_cluster:
            # 返回加权中心
            total_weight = sum(c['score'] for c in best_cluster)
            if total_weight > 0:
                weighted_x = sum(c['point'][0] * c['score'] for c in best_cluster) / total_weight
                weighted_y = sum(c['point'][1] * c['score'] for c in best_cluster) / total_weight
                return (int(weighted_x), int(weighted_y))
        
        return candidates[0]['point']
    
    def _cluster_candidates(self, candidates, distance_threshold=10):
        """聚类候选点"""
        clusters = []
        used = [False] * len(candidates)
        
        for i, candidate in enumerate(candidates):
            if used[i]:
                continue
            
            cluster = [candidate]
            used[i] = True
            point = candidate['point']
            
            for j, other_candidate in enumerate(candidates):
                if used[j]:
                    continue
                
                other_point = other_candidate['point']
                distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
                
                if distance <= distance_threshold:
                    cluster.append(other_candidate)
                    used[j] = True
            
            clusters.append(cluster)
        
        return clusters
    
    def _evaluate_cluster(self, cluster):
        """评估聚类质量"""
        if not cluster:
            return 0
        
        # 基础得分
        avg_score = np.mean([c['score'] for c in cluster])
        max_score = np.max([c['score'] for c in cluster])
        
        # 方法多样性
        methods = set(c['method'] for c in cluster)
        diversity_bonus = len(methods) * 0.1
        
        # 候选点数量
        count_bonus = min(len(cluster) / 4, 0.2)
        
        # 亮度检测优先级
        brightness_count = len([c for c in cluster if c['method'] == 'brightness'])
        brightness_bonus = min(brightness_count / len(cluster), 0.3)
        
        return avg_score * 0.4 + max_score * 0.3 + diversity_bonus + count_bonus + brightness_bonus

class LaserTracker:
    """激光点跟踪器"""
    
    def __init__(self, history_size=5):
        self.history = []
        self.history_size = history_size
        
    def update(self, detection):
        """更新检测结果"""
        if detection is not None:
            self.history.append(detection)
            if len(self.history) > self.history_size:
                self.history.pop(0)
            
            # 返回平滑后的位置
            if len(self.history) >= 3:
                # 使用加权平均，最新的检测权重更高
                weights = np.linspace(0.5, 1.0, len(self.history))
                weights = weights / np.sum(weights)
                
                avg_x = sum(p[0] * w for p, w in zip(self.history, weights))
                avg_y = sum(p[1] * w for p, w in zip(self.history, weights))
                return (int(avg_x), int(avg_y))
            else:
                return detection
        else:
            # 逐渐减少历史记录
            if self.history:
                self.history.pop(0)
            return self.history[-1] if self.history else None

def find_black_rectangle(img):
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    lower_black = np.array([0, 0, 0])
    upper_black = np.array([180, 255, 40])
    mask = cv2.inRange(hsv, lower_black, upper_black)
    
    # 形态学操作增强黑色区域
    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.dilate(mask, kernel, iterations=1)
    
    # 寻找轮廓
    contours, _ = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    
    # 寻找最大的四边形轮廓（假设黑色矩形是最大四边形）
    max_area = 0
    rectangle_contour = None
    
    for contour in contours:
        # 多边形逼近
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 寻找四边形
        if len(approx) == 4:
            area = cv2.contourArea(approx)
            if area > max_area:
                max_area = area
                rectangle_contour = approx
    
    if rectangle_contour is None:
        return None, None
    
    # 获取外角点（矩形轮廓的四个角点）
    outer_corners = rectangle_contour.reshape(-1, 2)
    
    # 计算内角点：通过缩小轮廓获取内矩形
    center = np.mean(outer_corners, axis=0)
    scaled_inner_corners = 0.9 * (outer_corners - center) + center
    inner_corners = scaled_inner_corners.astype(np.int32)
    
    return outer_corners, inner_corners

def find_red_laser(img):

    return None

def detect_features(img):
    """主函数：检测图片中的特征"""
    # 读取图像
    if img is None:
        print(f"无法读取图像: {img}")
        return
    
    # 创建结果图像副本
    result_img = img.copy()
    
    # 检测黑色矩形
    outer_corners, inner_corners = find_black_rectangle(img)
    
    if outer_corners is not None:
        print(f"黑色矩形外角点坐标: {outer_corners.tolist()}")
        
        # 在图像上标记外角点
        for i, (x, y) in enumerate(outer_corners):
            cv2.circle(result_img, (x, y), 8, (0, 255, 0), -1)  # 绿色点表示外角
            cv2.putText(result_img, f"O{i+1}", (x-20, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    if inner_corners is not None:
        print(f"黑色矩形内角点坐标: {inner_corners.tolist()}")
        
        # 在图像上标记内角点
        for i, (x, y) in enumerate(inner_corners):
            cv2.circle(result_img, (x, y), 6, (255, 0, 0), -1)  # 蓝色点表示内角
            cv2.putText(result_img, f"I{i+1}", (x-15, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
    
    # 检测红色激光点
    laser_point = find_red_laser(img)
    
    if laser_point is not None:
        x, y = laser_point
        print(f"红色激光点坐标: ({x}, {y})")
        
        # 在图像上标记激光点
        cv2.circle(result_img, (x, y), 10, (0, 0, 255), -1)  # 红色点表示激光点
        cv2.putText(result_img, "LASER", (x-40, y-20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
    return result_img

pre_time = time.perf_counter_ns()

# 初始化折线图相关变量
fps_history = []
timestamps = []
graph_width = 800
graph_height = 300
graph_margin = 50  # 图像边缘留白
max_history = 1000  # 最多保存的FPS数量
line_color = (255, 255, 0)  # 绿色折线

# 创建折线图画布（黑色背景）
graph_image = np.zeros((graph_height, graph_width, 3), dtype=np.uint8)

vc = cv2.VideoCapture(0)

vc.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减小缓冲区
# vc.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M','J','P','G')) # 使用MJPEG

vc.set(cv2.CAP_PROP_FRAME_WIDTH, 2560)
vc.set(cv2.CAP_PROP_FRAME_HEIGHT, 1440)

# vc.set(cv2.CAP_PROP_FPS, 30)

# print(vc.get(cv2.CAP_PROP_FPS))

# vc.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 0.25表示手动曝光模式
# exposure_time = 10  # 示例值（30ms），实际值需根据摄像头调整
# vc.set(cv2.CAP_PROP_EXPOSURE, exposure_time / 1000)  # OpenCV需要秒为单位

# 检查打开是否正确
if vc.isOpened():
    open, frame = vc.read()
else:
    open = False
    print('Failed to open camera')

# 创建两个窗口
cv2.namedWindow('Video Feed', cv2.WINDOW_NORMAL)
cv2.namedWindow('FPS Monitor', cv2.WINDOW_NORMAL)
cv2.resizeWindow('FPS Monitor', graph_width, graph_height)

while open:
    ret, frame = vc.read()
    real_time = time.perf_counter_ns()
    if frame is None:
        break
    if ret is True:
        # 计算当前FPS
        current_fps = 1000000000 / (real_time - pre_time)
        
        # 更新FPS历史数据
        fps_history.append(current_fps)
        timestamps.append(time.perf_counter_ns())
        
        # 移除10秒前的旧数据
        while fps_history and (timestamps[-1] - timestamps[0]) > 10000000000:  # 10秒
            fps_history.pop(0)
            timestamps.pop(0)
        
        # 在视频上显示当前FPS
        frame = detect_features(frame)
        cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.65, (0, 255, 0), 2)
        cv2.imshow('Video Feed', frame)
        
        # 准备FPS折线图
        graph_image.fill(0)  # 清空画布
        
        if len(fps_history) > 1:
            # 计算纵轴范围（FPS范围）
            min_fps = max(0, min(fps_history) * 0.9)
            max_fps = max(fps_history) * 1.1
            
            # 绘制坐标轴
            cv2.line(graph_image, 
                    (graph_margin, graph_margin), 
                    (graph_margin, graph_height - graph_margin), 
                    (128, 128, 128), 1)
            cv2.line(graph_image, 
                    (graph_margin, graph_height - graph_margin), 
                    (graph_width - graph_margin, graph_height - graph_margin), 
                    (128, 128, 128), 1)
            
            # 绘制参考线
            for i in range(0, int(max_fps) + 20, 10):
                y_pos = graph_height - graph_margin - int(
                    (i - min_fps) / (max_fps - min_fps) * (graph_height - 2 * graph_margin))
                if y_pos > graph_margin:
                    cv2.line(graph_image, 
                            (graph_margin, y_pos), 
                            (graph_width - graph_margin, y_pos), 
                            (50, 50, 50), 1)
                    cv2.putText(graph_image, str(i), 
                               (5, y_pos + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, 
                               (200, 200, 200), 1)
            
            # 绘制折线
            points = []
            for i, fps in enumerate(fps_history):
                # 计算点坐标
                x = int(graph_margin + i * (graph_width - 2 * graph_margin) / len(fps_history))
                y = graph_height - graph_margin - int(
                    (fps - min_fps) / (max_fps - min_fps) * (graph_height - 2 * graph_margin))
                points.append((x, y))
            
            # 连接所有点形成折线
            for i in range(1, len(points)):
                cv2.line(graph_image, points[i-1], points[i], line_color, 2)
            
            # 显示平均FPS
            avg_fps = sum(fps_history) / len(fps_history)
            cv2.putText(graph_image, f"Avg FPS: {avg_fps:.1f}", 
                       (graph_width - 200, 25), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (0, 255, 255), 1)
        
        # 显示当前FPS
        cv2.putText(graph_image, f"Current FPS: {current_fps:.1f}", 
                   (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        # 显示折线图
        cv2.imshow('FPS Monitor', graph_image)
        
        # 更新时间戳
        pre_time = time.perf_counter_ns()
        
        # 检查退出条件
        if cv2.waitKey(1) & 0xFF == 27:  # ESC键退出
            break

# 释放资源
vc.release()
cv2.destroyAllWindows()
