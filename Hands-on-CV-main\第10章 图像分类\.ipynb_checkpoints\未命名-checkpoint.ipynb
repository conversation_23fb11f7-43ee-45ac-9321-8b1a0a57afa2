{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第10章 图像分类"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在前面的章节中，我们学会了滤波并在此基础上实现了模板匹配、边缘检测等一系列后续处理方法；当我们学习完如何检测图像中的角点等特征点之后，我们又进一步学习了SIFT算法，并利用SIFT实现图像拼接。至此，我们已经完整的学习了图像处理的基础知识。接下来，我们将在这些知识的基础上，学习更高阶的计算机视觉————图像语义理解部分。\n", "\n", "我们将学习....。（第二大章序）\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10.1 简介"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.7"}}, "nbformat": 4, "nbformat_minor": 2}