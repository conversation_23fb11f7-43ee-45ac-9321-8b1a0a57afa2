#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的基于标注数据的激光点检测器
基于分析结果调整参数，提高检测率
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime

class ImprovedAnnotationDetector:
    def __init__(self, annotation_file="laser_annotations/quick_annotations_20250723_192904.json"):
        self.annotation_file = annotation_file
        # 基于分析结果使用更宽松的参数
        self.params = {
            'min_brightness': 120,  # 降低亮度要求
            'brightness_threshold_offset': 25,  # 增加阈值偏移
            'min_contrast': 3,  # 降低对比度要求
            'min_red_component': 80,  # 降低红色要求
            'min_red_dominance': 5,  # 降低红色优势要求
            'min_area': 1,
            'max_area': 200,  # 增加最大面积
            'min_circularity': 0.1,  # 降低圆形度要求
            'score_threshold': 0.2  # 降低得分阈值
        }
        print("使用改进的宽松参数")
        
    def detect_laser_point(self, image):
        """检测激光点"""
        if image is None:
            return None
        
        candidates = []
        
        # 方法1: 宽松的亮度检测
        bright_candidates = self._relaxed_brightness_detection(image)
        candidates.extend(bright_candidates)
        
        # 方法2: 宽松的红色检测
        red_candidates = self._relaxed_red_detection(image)
        candidates.extend(red_candidates)
        
        # 方法3: 边缘检测
        edge_candidates = self._edge_detection(image)
        candidates.extend(edge_candidates)
        
        # 选择最佳候选点
        return self._select_best_candidate(candidates)
    
    def _relaxed_brightness_detection(self, image):
        """宽松的亮度检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        max_val = np.max(gray)
        mean_val = np.mean(gray)
        
        # 使用多个阈值
        thresholds = [
            max_val - 15,
            max_val - 25,
            max_val - 35,
            mean_val + 30,
            mean_val + 20
        ]
        
        for threshold in thresholds:
            if threshold < self.params['min_brightness']:
                continue
                
            _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
            
            # 轻微的形态学操作
            kernel = np.ones((3, 3), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if self.params['min_area'] <= area <= self.params['max_area']:
                    M = cv2.moments(contour)
                    if M["m00"] != 0:
                        cx = int(M["m10"] / M["m00"])
                        cy = int(M["m01"] / M["m00"])
                        
                        if self._verify_laser_features(gray, (cx, cy)):
                            # 计算得分
                            brightness_score = gray[cy, cx] / 255.0
                            area_score = min(area / 50.0, 1.0)
                            
                            # 圆形度
                            perimeter = cv2.arcLength(contour, True)
                            if perimeter > 0:
                                circularity = 4 * np.pi * area / (perimeter ** 2)
                            else:
                                circularity = 0
                            
                            score = brightness_score * 0.4 + area_score * 0.3 + circularity * 0.3
                            
                            if score >= self.params['score_threshold']:
                                candidates.append({
                                    'point': (cx, cy),
                                    'score': score,
                                    'method': 'brightness'
                                })
        
        return candidates
    
    def _relaxed_red_detection(self, image):
        """宽松的红色检测"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 更宽松的红色范围
        red_ranges = [
            ([0, 30, 50], [15, 255, 255]),
            ([165, 30, 50], [180, 255, 255]),
            ([0, 50, 80], [20, 255, 255]),
            ([160, 50, 80], [180, 255, 255])
        ]
        
        combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)
        
        for lower, upper in red_ranges:
            mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
            combined_mask = cv2.bitwise_or(combined_mask, mask)
        
        # 宽松的亮度掩码
        _, brightness_mask = cv2.threshold(gray, self.params['min_brightness'], 255, cv2.THRESH_BINARY)
        
        # 融合掩码
        final_mask = cv2.bitwise_and(combined_mask, brightness_mask)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.params['min_area'] <= area <= self.params['max_area']:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    score = min(area / 60.0, 1.0)
                    candidates.append({
                        'point': (cx, cy),
                        'score': score,
                        'method': 'red'
                    })
        
        return candidates
    
    def _edge_detection(self, image):
        """边缘检测方法"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # Canny边缘检测
        edges = cv2.Canny(blurred, 50, 150)
        
        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 3 <= area <= 100:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 检查该点的亮度
                    if gray[cy, cx] > self.params['min_brightness']:
                        score = gray[cy, cx] / 255.0 * 0.5 + min(area / 50.0, 1.0) * 0.5
                        candidates.append({
                            'point': (cx, cy),
                            'score': score,
                            'method': 'edge'
                        })
        
        return candidates
    
    def _verify_laser_features(self, gray, point):
        """宽松的特征验证"""
        x, y = point
        h, w = gray.shape
        
        if x < 3 or y < 3 or x >= w-3 or y >= h-3:
            return False
        
        center_val = gray[y, x]
        if center_val < self.params['min_brightness']:
            return False
        
        # 宽松的对比度检查
        local_region = gray[y-2:y+3, x-2:x+3]
        avg_surrounding = np.mean(local_region)
        contrast = center_val - avg_surrounding
        
        if contrast < self.params['min_contrast']:
            return False
        
        return True
    
    def _select_best_candidate(self, candidates):
        """选择最佳候选点"""
        if not candidates:
            return None
        
        if len(candidates) == 1:
            return candidates[0]['point']
        
        # 按得分排序
        candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)
        
        # 聚类分析
        clusters = self._cluster_candidates(candidates)
        
        # 选择最佳聚类
        best_cluster = None
        best_score = 0
        
        for cluster in clusters:
            cluster_score = self._evaluate_cluster(cluster)
            if cluster_score > best_score:
                best_score = cluster_score
                best_cluster = cluster
        
        if best_cluster:
            # 返回聚类中心（加权平均）
            total_weight = sum(c['score'] for c in best_cluster)
            if total_weight > 0:
                weighted_x = sum(c['point'][0] * c['score'] for c in best_cluster) / total_weight
                weighted_y = sum(c['point'][1] * c['score'] for c in best_cluster) / total_weight
                return (int(weighted_x), int(weighted_y))
        
        return candidates[0]['point']
    
    def _cluster_candidates(self, candidates, distance_threshold=12):
        """聚类候选点"""
        clusters = []
        used = [False] * len(candidates)
        
        for i, candidate in enumerate(candidates):
            if used[i]:
                continue
            
            cluster = [candidate]
            used[i] = True
            point = candidate['point']
            
            for j, other_candidate in enumerate(candidates):
                if used[j]:
                    continue
                
                other_point = other_candidate['point']
                distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
                
                if distance <= distance_threshold:
                    cluster.append(other_candidate)
                    used[j] = True
            
            clusters.append(cluster)
        
        return clusters
    
    def _evaluate_cluster(self, cluster):
        """评估聚类质量"""
        if not cluster:
            return 0
        
        # 基础得分
        avg_score = np.mean([c['score'] for c in cluster])
        max_score = np.max([c['score'] for c in cluster])
        
        # 方法多样性奖励
        methods = set(c['method'] for c in cluster)
        diversity_bonus = len(methods) * 0.1
        
        # 候选点数量奖励
        count_bonus = min(len(cluster) / 5, 0.2)
        
        return avg_score * 0.5 + max_score * 0.3 + diversity_bonus + count_bonus

def test_improved_detector():
    """测试改进的检测器"""
    detector = ImprovedAnnotationDetector()
    
    cap = cv2.VideoCapture('output.mp4')
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("开始测试改进的检测器...")
    
    import time
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        frame = cv2.resize(frame, (640, 480))
        
        # 保存原始帧用于显示
        display_frame = frame.copy()
        
        # 提取ROI区域
        roi_frame = frame[65:410, 260:470, :]
        roi_offset = (260, 65)
        
        start_time = time.time()
        
        # 检测激光点
        laser_point = detector.detect_laser_point(roi_frame)
        
        end_time = time.time()
        total_time += (end_time - start_time)
        
        if laser_point:
            detection_count += 1
            # 转换回原图坐标
            x = laser_point[0] + roi_offset[0]
            y = laser_point[1] + roi_offset[1]
            
            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y}) [ROI: ({laser_point[0]}, {laser_point[1]})]")
            
            # 在原图上绘制检测结果
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(display_frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 绘制ROI区域边界
            cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
            cv2.putText(display_frame, "ROI", (265, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 显示FPS和状态信息
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(display_frame, f"FPS: {current_fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # 显示检测信息
            detection_rate = detection_count / frame_count * 100
            cv2.putText(display_frame, f"Detection: {detection_rate:.1f}%", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 每3帧显示一次
        if frame_count % 3 == 0:
            cv2.imshow('Improved Annotation-Based Detection', display_frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 改进检测器结果 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")

if __name__ == "__main__":
    test_improved_detector()
