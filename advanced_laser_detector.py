#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高精度激光点检测器 - 针对白色方形纸板区域
使用复杂算法提高检测精度，适用于6Tops INT8算力设备
"""

import cv2
import numpy as np
from scipy import ndimage
from sklearn.cluster import DBSCAN
import time

def detect_laser_point_advanced(image):
    """
    高精度激光点检测 - 专门针对白色方形纸板区域
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 步骤1: 检测并提取白色方形纸板区域
    paper_region, paper_mask, transform_matrix = _detect_paper_region(image)
    if paper_region is None:
        # 如果无法检测到纸板，使用全图检测
        paper_region = image
        paper_mask = np.ones(image.shape[:2], dtype=np.uint8) * 255
        transform_matrix = None
    
    # 步骤2: 在纸板区域内进行多算法融合检测
    laser_candidates = _multi_algorithm_detection(paper_region, paper_mask)
    
    # 步骤3: 使用机器学习方法筛选最佳候选
    best_candidate = _ml_candidate_selection(paper_region, laser_candidates)
    
    # 步骤4: 将坐标转换回原图坐标系
    if best_candidate and transform_matrix is not None:
        return _transform_point_back(best_candidate, transform_matrix, image.shape)
    
    return best_candidate

def _detect_paper_region(image):
    """检测白色方形纸板区域"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 使用自适应阈值检测白色区域
    mean_brightness = np.mean(gray)
    threshold = min(200, mean_brightness + 30)
    _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
    
    # 形态学操作清理噪声
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None, None, None
    
    # 寻找最大的近似矩形轮廓
    best_contour = None
    max_area = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 10000:  # 纸板应该有一定大小
            continue
        
        # 多边形逼近
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 检查是否为四边形且面积足够大
        if len(approx) == 4 and area > max_area:
            max_area = area
            best_contour = approx
    
    if best_contour is None:
        return None, None, None
    
    # 透视变换提取纸板区域
    paper_region, transform_matrix = _extract_paper_region(image, best_contour)
    
    # 创建纸板区域掩码
    paper_mask = np.ones(paper_region.shape[:2], dtype=np.uint8) * 255
    
    return paper_region, paper_mask, transform_matrix

def _extract_paper_region(image, contour):
    """使用透视变换提取纸板区域"""
    # 获取四个角点
    points = contour.reshape(4, 2).astype(np.float32)
    
    # 排序角点：左上、右上、右下、左下
    rect = _order_points(points)
    
    # 计算目标矩形尺寸
    width_a = np.sqrt(((rect[2][0] - rect[3][0]) ** 2) + ((rect[2][1] - rect[3][1]) ** 2))
    width_b = np.sqrt(((rect[1][0] - rect[0][0]) ** 2) + ((rect[1][1] - rect[0][1]) ** 2))
    max_width = max(int(width_a), int(width_b))
    
    height_a = np.sqrt(((rect[1][0] - rect[2][0]) ** 2) + ((rect[1][1] - rect[2][1]) ** 2))
    height_b = np.sqrt(((rect[0][0] - rect[3][0]) ** 2) + ((rect[0][1] - rect[3][1]) ** 2))
    max_height = max(int(height_a), int(height_b))
    
    # 目标点
    dst = np.array([
        [0, 0],
        [max_width - 1, 0],
        [max_width - 1, max_height - 1],
        [0, max_height - 1]
    ], dtype=np.float32)
    
    # 计算透视变换矩阵
    transform_matrix = cv2.getPerspectiveTransform(rect, dst)
    
    # 应用透视变换
    warped = cv2.warpPerspective(image, transform_matrix, (max_width, max_height))
    
    return warped, transform_matrix

def _order_points(pts):
    """排序四个点：左上、右上、右下、左下"""
    rect = np.zeros((4, 2), dtype=np.float32)
    
    # 左上角点的和最小，右下角点的和最大
    s = pts.sum(axis=1)
    rect[0] = pts[np.argmin(s)]
    rect[2] = pts[np.argmax(s)]
    
    # 右上角点的差最小，左下角点的差最大
    diff = np.diff(pts, axis=1)
    rect[1] = pts[np.argmin(diff)]
    rect[3] = pts[np.argmax(diff)]
    
    return rect

def _multi_algorithm_detection(image, mask):
    """多算法融合检测激光点候选"""
    candidates = []
    
    # 算法1: 高斯拉普拉斯检测
    candidates.extend(_log_detection(image, mask))
    
    # 算法2: 多尺度模板匹配
    candidates.extend(_multi_scale_template_matching(image, mask))
    
    # 算法3: 基于梯度的检测
    candidates.extend(_gradient_based_detection(image, mask))
    
    # 算法4: 颜色空间融合检测
    candidates.extend(_color_space_fusion_detection(image, mask))
    
    # 算法5: 频域分析检测
    candidates.extend(_frequency_domain_detection(image, mask))
    
    return candidates

def _log_detection(image, mask):
    """高斯拉普拉斯检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    candidates = []
    # 多尺度LoG检测
    for sigma in [1.0, 1.5, 2.0, 2.5]:
        # 高斯拉普拉斯
        log = ndimage.gaussian_laplace(gray.astype(np.float32), sigma)
        log = np.abs(log)
        
        # 应用掩码
        log = cv2.bitwise_and(log.astype(np.uint8), mask)
        
        # 查找局部最大值
        local_maxima = ndimage.maximum_filter(log, size=5) == log
        local_maxima = local_maxima & (log > np.percentile(log[mask > 0], 95))
        
        # 提取候选点
        y_coords, x_coords = np.where(local_maxima)
        for x, y in zip(x_coords, y_coords):
            candidates.append({
                'point': (x, y),
                'score': float(log[y, x]),
                'method': 'LoG',
                'scale': sigma
            })
    
    return candidates

def _multi_scale_template_matching(image, mask):
    """多尺度模板匹配"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 创建多个激光点模板
    templates = _create_laser_templates()
    
    for template_name, template in templates.items():
        for scale in [0.5, 0.75, 1.0, 1.25, 1.5]:
            # 缩放模板
            scaled_template = cv2.resize(template, None, fx=scale, fy=scale)
            
            if scaled_template.shape[0] > gray.shape[0] or scaled_template.shape[1] > gray.shape[1]:
                continue
            
            # 模板匹配
            result = cv2.matchTemplate(gray, scaled_template, cv2.TM_CCOEFF_NORMED)
            
            # 应用掩码
            h, w = scaled_template.shape
            valid_mask = mask[h//2:-h//2+1, w//2:-w//2+1] if h < mask.shape[0] and w < mask.shape[1] else mask
            if valid_mask.shape == result.shape:
                result = result * (valid_mask / 255.0)
            
            # 查找高分匹配
            locations = np.where(result >= 0.7)
            for pt in zip(*locations[::-1]):
                x, y = pt[0] + w//2, pt[1] + h//2
                candidates.append({
                    'point': (x, y),
                    'score': float(result[pt[1], pt[0]]),
                    'method': 'Template',
                    'template': template_name,
                    'scale': scale
                })
    
    return candidates

def _create_laser_templates():
    """创建激光点模板"""
    templates = {}
    
    # 圆形模板
    for radius in [3, 5, 7, 10]:
        template = np.zeros((radius*4, radius*4), dtype=np.uint8)
        cv2.circle(template, (radius*2, radius*2), radius, 255, -1)
        # 添加高斯模糊模拟激光点的光晕效果
        template = cv2.GaussianBlur(template, (radius//2*2+1, radius//2*2+1), radius/3)
        templates[f'circle_{radius}'] = template
    
    # 十字形模板
    for size in [7, 11, 15]:
        template = np.zeros((size, size), dtype=np.uint8)
        cv2.line(template, (size//2, 0), (size//2, size-1), 255, 1)
        cv2.line(template, (0, size//2), (size-1, size//2), 255, 1)
        templates[f'cross_{size}'] = template
    
    return templates

def _gradient_based_detection(image, mask):
    """基于梯度的检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 计算梯度
    grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
    
    # 梯度幅值和方向
    magnitude = np.sqrt(grad_x**2 + grad_y**2)
    
    # 应用掩码
    magnitude = cv2.bitwise_and(magnitude.astype(np.uint8), mask)
    
    # 查找梯度峰值
    threshold = np.percentile(magnitude[mask > 0], 90)
    peaks = magnitude > threshold
    
    # 形态学操作找到连通区域
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    peaks = cv2.morphologyEx(peaks.astype(np.uint8), cv2.MORPH_CLOSE, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(peaks, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for contour in contours:
        if cv2.contourArea(contour) < 5:
            continue
        
        # 计算质心
        M = cv2.moments(contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            candidates.append({
                'point': (cx, cy),
                'score': float(magnitude[cy, cx]),
                'method': 'Gradient'
            })
    
    return candidates

def _ml_candidate_selection(image, candidates):
    """使用机器学习方法选择最佳候选点"""
    if not candidates:
        return None

    # 聚类去除重复候选点
    points = np.array([c['point'] for c in candidates])

    if len(points) == 1:
        return candidates[0]['point']

    # 使用DBSCAN聚类
    clustering = DBSCAN(eps=10, min_samples=1).fit(points)
    labels = clustering.labels_

    # 为每个聚类计算综合得分
    cluster_scores = {}
    for i, label in enumerate(labels):
        if label not in cluster_scores:
            cluster_scores[label] = []
        cluster_scores[label].append(candidates[i])

    # 选择最佳聚类
    best_cluster = None
    best_score = 0

    for label, cluster_candidates in cluster_scores.items():
        # 计算聚类的综合得分
        score = _calculate_cluster_score(image, cluster_candidates)
        if score > best_score:
            best_score = score
            best_cluster = cluster_candidates

    if best_cluster:
        # 返回聚类中心点
        points = np.array([c['point'] for c in best_cluster])
        center_x = int(np.mean(points[:, 0]))
        center_y = int(np.mean(points[:, 1]))
        return (center_x, center_y)

    return None

def _calculate_cluster_score(image, cluster_candidates):
    """计算聚类的综合得分"""
    if not cluster_candidates:
        return 0

    # 基础得分：候选点数量和平均得分
    num_candidates = len(cluster_candidates)
    avg_score = np.mean([c['score'] for c in cluster_candidates])

    # 方法多样性得分
    methods = set(c['method'] for c in cluster_candidates)
    diversity_score = len(methods)

    # 位置一致性得分
    points = np.array([c['point'] for c in cluster_candidates])
    if len(points) > 1:
        distances = np.sqrt(np.sum((points - np.mean(points, axis=0))**2, axis=1))
        consistency_score = 1.0 / (1.0 + np.std(distances))
    else:
        consistency_score = 1.0

    # 图像特征得分
    center_point = tuple(np.mean(points, axis=0).astype(int))
    feature_score = _calculate_point_features(image, center_point)

    # 综合得分
    total_score = (avg_score * 0.3 +
                  diversity_score * 0.2 +
                  consistency_score * 0.2 +
                  feature_score * 0.2 +
                  min(num_candidates / 3, 1.0) * 0.1)

    return total_score

def _calculate_point_features(image, point):
    """计算点的图像特征得分"""
    x, y = point
    h, w = image.shape[:2]

    if x < 5 or y < 5 or x >= w-5 or y >= h-5:
        return 0

    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 局部亮度特征
    local_region = gray[y-5:y+6, x-5:x+6]
    center_brightness = gray[y, x]
    avg_brightness = np.mean(local_region)
    brightness_contrast = center_brightness - avg_brightness

    # 局部梯度特征
    grad_x = cv2.Sobel(local_region, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(local_region, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(grad_x[5, 5]**2 + grad_y[5, 5]**2)

    # 圆形度特征（检查周围是否呈圆形分布）
    angles = np.linspace(0, 2*np.pi, 8, endpoint=False)
    radius = 3
    circle_points = []
    for angle in angles:
        px = int(x + radius * np.cos(angle))
        py = int(y + radius * np.sin(angle))
        if 0 <= px < w and 0 <= py < h:
            circle_points.append(gray[py, px])

    if circle_points:
        circle_variance = np.var(circle_points)
        circularity_score = 1.0 / (1.0 + circle_variance / 100.0)
    else:
        circularity_score = 0

    # 综合特征得分
    feature_score = (brightness_contrast / 255.0 * 0.4 +
                    gradient_magnitude / 255.0 * 0.3 +
                    circularity_score * 0.3)

    return max(0, min(1, feature_score))

def _transform_point_back(point, transform_matrix, original_shape):
    """将点坐标从纸板坐标系转换回原图坐标系"""
    if transform_matrix is None:
        return point

    x, y = point
    # 计算逆变换矩阵
    inv_matrix = cv2.invert(transform_matrix)[1]

    # 转换点坐标
    point_homogeneous = np.array([[[x, y]]], dtype=np.float32)
    transformed_point = cv2.perspectiveTransform(point_homogeneous, inv_matrix)

    new_x, new_y = transformed_point[0][0]

    # 确保坐标在图像范围内
    h, w = original_shape[:2]
    new_x = max(0, min(w-1, int(new_x)))
    new_y = max(0, min(h-1, int(new_y)))

    return (new_x, new_y)

def test_advanced_detector():
    """测试高精度检测器"""
    cap = cv2.VideoCapture('output.mp4')

    if not cap.isOpened():
        print("无法打开视频文件")
        return

    frame_count = 0
    detection_count = 0
    total_time = 0

    print("开始高精度激光点检测测试...")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        frame_count += 1

        # 调整到目标分辨率
        frame = cv2.resize(frame, (640, 480))

        start_time = time.time()

        # 高精度检测
        laser_point = detect_laser_point_advanced(frame)

        end_time = time.time()
        total_time += (end_time - start_time)

        if laser_point:
            detection_count += 1
            x, y = laser_point

            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y})")

            # 绘制检测结果
            cv2.circle(frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(frame, f"({x}, {y})", (x+15, y-15),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # 显示FPS
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)

        cv2.imshow('Advanced Laser Detection', frame)

        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s') and laser_point:
            # 保存当前帧
            filename = f"advanced_detection_{frame_count:04d}.jpg"
            cv2.imwrite(filename, frame)
            print(f"保存图像: {filename}")

    cap.release()
    cv2.destroyAllWindows()

    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0

    print(f"\n=== 高精度检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")
    print(f"目标性能: 20 FPS @ 640x480")
    print(f"性能达标: {'是' if avg_fps >= 20 else '否'}")

if __name__ == "__main__":
    print("高精度激光点检测器")
    print("使用复杂算法提高检测精度")
    print("目标: 20 FPS @ 640x480 分辨率")
    test_advanced_detector()

def _color_space_fusion_detection(image, mask):
    """颜色空间融合检测"""
    candidates = []
    
    # HSV检测
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 红色范围检测
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([170, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)
    red_mask = cv2.bitwise_and(red_mask, mask)
    
    # LAB检测
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    # A通道增强红色
    a_enhanced = cv2.normalize(a, None, 0, 255, cv2.NORM_MINMAX)
    _, a_mask = cv2.threshold(a_enhanced, 140, 255, cv2.THRESH_BINARY)
    a_mask = cv2.bitwise_and(a_mask, mask)
    
    # 融合多个颜色空间的结果
    combined_mask = cv2.bitwise_or(red_mask, a_mask)
    
    # 查找轮廓
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if 3 <= area <= 200:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                candidates.append({
                    'point': (cx, cy),
                    'score': area,
                    'method': 'ColorFusion'
                })
    
    return candidates

def _frequency_domain_detection(image, mask):
    """频域分析检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 应用掩码
    masked_gray = cv2.bitwise_and(gray, mask)
    
    # FFT变换
    f_transform = np.fft.fft2(masked_gray)
    f_shift = np.fft.fftshift(f_transform)
    
    # 高通滤波器（突出小的亮点）
    rows, cols = masked_gray.shape
    crow, ccol = rows // 2, cols // 2
    
    # 创建高通滤波器
    mask_freq = np.ones((rows, cols), np.uint8)
    r = 30
    center = [crow, ccol]
    x, y = np.ogrid[:rows, :cols]
    mask_area = (x - center[0]) ** 2 + (y - center[1]) ** 2 <= r*r
    mask_freq[mask_area] = 0
    
    # 应用滤波器
    f_shift_filtered = f_shift * mask_freq
    
    # 逆变换
    f_ishift = np.fft.ifftshift(f_shift_filtered)
    img_back = np.fft.ifft2(f_ishift)
    img_back = np.abs(img_back)
    
    # 归一化
    img_back = cv2.normalize(img_back, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
    
    # 查找峰值
    threshold = np.percentile(img_back[mask > 0], 95)
    peaks = img_back > threshold
    
    # 查找连通区域
    contours, _ = cv2.findContours(peaks.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for contour in contours:
        if cv2.contourArea(contour) >= 3:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                candidates.append({
                    'point': (cx, cy),
                    'score': float(img_back[cy, cx]),
                    'method': 'Frequency'
                })
    
    return candidates
