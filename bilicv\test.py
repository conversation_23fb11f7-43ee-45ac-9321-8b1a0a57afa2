import cv2
import numpy as np

def segment_tomatoes(img):
    """
    对番茄进行颜色分割（红色 + 绿色），返回二值掩码
    """
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

    # 红色：HSV 分两段
    lower_red1 = np.array([0, 100, 100])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([140, 100, 100])
    upper_red2 = np.array([190, 255, 255])

    # 绿色
    lower_green = np.array([35,  80,  80])
    upper_green = np.array([85, 255, 255])

    mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask_red  = cv2.bitwise_or(mask_red1, mask_red2)

    mask_green = cv2.inRange(hsv, lower_green, upper_green)

    # mask = cv2.bitwise_or(mask_red, mask_green)
    mask = mask_red

    # 形态学开闭操作去噪声
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7,7))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN,  kernel, iterations=1)

    return mask

def draw_contours_and_points(img, contours):
    """
    在 img 上绘制 contours 及其边界点
    """
    out = img.copy()
    for i, cnt in enumerate(contours):
        # 随机颜色
        color = tuple(int(c) for c in np.random.randint(0,255,3))
        # 画整体轮廓
        cv2.drawContours(out, [cnt], -1, color, 2)

        # 画每个边界点
        for pt in cnt.reshape(-1, 2):
            cv2.circle(out, tuple(pt), 2, color, -1)

    return out

def main():
    # 1. 读图
    img = cv2.imread('Tian.jpg')
    if img is None:
        print("无法读取图像，请检查路径")
        return

    # 2. 分割番茄
    mask = segment_tomatoes(img)

    # 3. 找轮廓
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

    # 4. 绘制
    result = draw_contours_and_points(img, contours)

    # 5. 显示 & 保存
    cv2.imshow('Tomato Boundaries', result)
    cv2.imwrite('tomato_boundaries.png', result)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == '__main__':
    main()
