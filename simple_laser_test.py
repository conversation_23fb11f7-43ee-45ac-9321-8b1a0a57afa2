#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的激光点检测测试
"""

import cv2
import numpy as np

def detect_laser_point_simple(image):
    """
    简化的激光点检测函数
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 方法1: 基于亮度的检测
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 查找最亮的区域
    _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        # 选择面积最大的轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(largest_contour)
        
        if 5 <= area <= 500:
            M = cv2.moments(largest_contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                return (cx, cy)
    
    # 方法2: HSV颜色检测
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 红色范围
    lower_red1 = np.array([0, 100, 100])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([170, 100, 100])
    upper_red2 = np.array([180, 255, 255])
    
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask = cv2.bitwise_or(mask1, mask2)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        largest_contour = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(largest_contour)
        
        if 3 <= area <= 300:
            M = cv2.moments(largest_contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                return (cx, cy)
    
    return None

def test_video():
    """测试视频中的激光点检测"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    frame_count = 0
    detection_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 检测激光点
        laser_point = detect_laser_point_simple(frame)
        
        if laser_point:
            detection_count += 1
            x, y = laser_point
            print(f"Frame {frame_count}: 检测到激光点 ({x}, {y})")
            
            # 在图像上标记
            cv2.circle(frame, (x, y), 10, (0, 255, 0), 2)
            cv2.putText(frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            print(f"Frame {frame_count}: 未检测到激光点")
        
        # 显示结果
        cv2.imshow('Laser Detection Test', frame)
        
        # 按ESC退出，按空格暂停
        key = cv2.waitKey(30) & 0xFF
        if key == 27:  # ESC
            break
        elif key == 32:  # Space
            cv2.waitKey(0)
    
    cap.release()
    cv2.destroyAllWindows()
    
    print(f"\n总结:")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")

if __name__ == "__main__":
    test_video()
