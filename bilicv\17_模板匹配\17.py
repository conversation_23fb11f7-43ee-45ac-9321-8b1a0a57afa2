# 250317

import cv2
import numpy as np
from sympy.simplify.simplify import bottom_up

img = cv2.imread("bilicv/img03.jpg")
img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

template = cv2.imread("bilicv/img04.jpg", cv2.IMREAD_GRAYSCALE)

h, w = template.shape[:2]

res = cv2.matchTemplate(img_gray, template, cv2.TM_CCOEFF_NORMED)
threshold = 0.9
loc = np.where(res >= threshold)
for pt in zip(*loc[::-1]):
    bottom_right = (pt[0]+w, pt[1]+h)
    show = cv2.rectangle(img, pt, bottom_right, (0, 0, 255), 1)

cv2.imshow("a", show)
cv2.waitKey(0)
cv2.destroyAllWindows()
