# 250316

import cv2
from matplotlib.pyplot import imshow

img01 = cv2.imread("bilicv/img01.jpg")

blur = cv2.blur(img01, (5,5))
# cv2.imshow("a", blur)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

box = cv2.boxFilter(img01, -1, (3, 3), normalize=False)
# cv2.imshow("a", box)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

gaussian = cv2.GaussianBlur(img01, (3, 3), 1)
# cv2.imshow("a", gaussian)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

medium = cv2.medianBlur(img01, 3)
cv2.imshow("a", medium)
cv2.waitKey(0)
cv2.destroyAllWindows()
