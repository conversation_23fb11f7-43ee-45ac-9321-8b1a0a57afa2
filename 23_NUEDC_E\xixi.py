# XIXIXIXI - 优化版激光点检测

import cv2
import time
import numpy as np

# import serial

# ser = serial.Serial(port="COMxxx", baudrate=115200)

def detect_laser_sift(image, template_path, min_match_count=2):
    template = cv2.imread(template_path, cv2.IMREAD_COLOR)
    if template is None:
        raise FileNotFoundError(f"无法读取模板: {template_path}")

    sift = cv2.SIFT_create()
    kp1, des1 = sift.detectAndCompute(template, None)
    kp2, des2 = sift.detectAndCompute(image, None)

    if des1 is None or des2 is None:
        return None

    bf = cv2.BFMatcher()
    matches = bf.knnMatch(des1, des2, k=2)

    good = []
    for m, n in matches:
        if m.distance < 0.7 * n.distance:
            good.append(m)

    if len(good) >= min_match_count:
        pts = np.float32([kp2[m.trainIdx].pt for m in good])
        avg_pt = np.mean(pts, axis=0)
        return (int(avg_pt[0]), int(avg_pt[1]))
    else:
        return None

def detect_red_laser(image):
    pt = detect_laser_sift(image, r"red_point.jpg")
    if pt is not None:
        return pt
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    lower1 = np.array([0, 100, 180])
    upper1 = np.array([10, 255, 255])
    lower2 = np.array([160, 100, 180])
    upper2 = np.array([180, 255, 255])
    mask1 = cv2.inRange(hsv, lower1, upper1)
    mask2 = cv2.inRange(hsv, lower2, upper2)
    mask = cv2.bitwise_or(mask1, mask2)
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    cnts, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if cnts:
        c = max(cnts, key=cv2.contourArea)
        if cv2.contourArea(c) > 5:
            M = cv2.moments(c)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                return (cx, cy)
    return None

def detect_green_laser(image):
    pt = detect_laser_sift(image, "23_NUEDC_E\red_point.jpg")
    if pt is not None:
        return pt
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    lower1 = np.array([0, 100, 180])
    upper1 = np.array([10, 255, 255])
    lower2 = np.array([160, 100, 180])
    upper2 = np.array([180, 255, 255])
    mask1 = cv2.inRange(hsv, lower1, upper1)
    mask2 = cv2.inRange(hsv, lower2, upper2)
    mask = cv2.bitwise_or(mask1, mask2)
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5,5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    cnts, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if cnts:
        c = max(cnts, key=cv2.contourArea)
        if cv2.contourArea(c) > 5:
            M = cv2.moments(c)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                return (cx, cy)
    return None


def detect_quadrilateral(img, cv_show):
    if img is None:
        raise FileNotFoundError(f"无法读取图像")
    original = img.copy()
    
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (7, 7), 0)
    
    _, thresh = cv2.threshold(blurred, 120, 255, cv2.THRESH_BINARY_INV)
    
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if not contours:
        raise ValueError("未检测到轮廓")
    
    quad_contour = None
    max_area = 0
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 500:
            continue
            
        peri = cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, 0.02 * peri, True)
        
        if len(approx) == 4 and area > max_area:
            quad_contour = approx.reshape(4, 2)
            max_area = area
    
    if quad_contour is None:
        raise ValueError("未检测到四边形轮廓")
    
    rect_points = order_points(quad_contour)
    
    colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 255, 255)]  # 红、绿、蓝、黄
    
    cv2.drawContours(img, [np.int_(rect_points)], -1, (255, 0, 255), 3)
    
    red_point = detect_red_laser(img)
    # green_point = detect_green_laser(img)

    green_point = None
    
    if cv_show is True:
        for i, (x, y) in enumerate(rect_points):
            cv2.circle(img, (int(x), int(y)), 10, colors[i], -1)
            cv2.putText(img, f"C{i+1}", (int(x) + 15, int(y)), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, colors[i], 2)
        
        if red_point:
            cv2.circle(img, red_point, 20, (0, 0, 0), 2)
            cv2.circle(img, red_point, 15, (0, 0, 255), -1)
            cv2.circle(img, red_point, 10, (255, 255, 255), -1)
            cv2.putText(img, "Red Laser", (red_point[0] - 30, red_point[1] - 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        if green_point:
            cv2.circle(img, green_point, 20, (0, 0, 0), 2)
            cv2.circle(img, green_point, 15, (0, 255, 0), -1)
            cv2.circle(img, green_point, 10, (255, 255, 255), -1)
            cv2.putText(img, "Green Laser", (green_point[0] - 40, green_point[1] - 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)   
    
    return rect_points, red_point, green_point

def order_points(pts):
    rect = np.zeros((4, 2), dtype="float32")
    
    center = pts.mean(axis=0)
    
    diff = pts - center
    angles = np.arctan2(diff[:, 1], diff[:, 0])
    sorted_indices = np.argsort(angles)
    sorted_pts = pts[sorted_indices]
    s = sorted_pts.sum(axis=1)
    rect[0] = sorted_pts[np.argmin(s)]
    rect[2] = sorted_pts[np.argmax(s)]
    
    diff = sorted_pts - center
    remaining = [pt for pt in sorted_pts if (pt != rect[0]).any() and (pt != rect[2]).any()]
    
    if len(remaining) == 2:
        if remaining[0][0] > center[0]:
            rect[1] = remaining[0]
            rect[3] = remaining[1]
        else:
            rect[1] = remaining[1]
            rect[3] = remaining[0]
    
    return rect

if __name__ == "__main__":
    pre_time = time.perf_counter_ns()

    vc = cv2.VideoCapture(1)
    vc.set(cv2.CAP_PROP_FPS, 30)

    # vc = cv2.VideoCapture(r'23_NUEDC_E\test1.mp4')

    if vc.isOpened():
        open, frame = vc.read()
    else:
        open = False
        print('False')

    while open:
        ret, frame = vc.read()
        real_time = time.perf_counter_ns()
        if frame is None:
            break
        if ret is True:
            # frame = frame[250:1200, 1035:1915, ]
            fps = 1000000000 / (real_time - pre_time)
            try:                  
                corner_points, red_laser, green_laser = detect_quadrilateral(frame, True)
                    
                print("检测到的四边形角点坐标:")
                print(f"左上角 (C1): ({corner_points[0][0]:.1f}, {corner_points[0][1]:.1f})")
                print(f"右上角 (C2): ({corner_points[1][0]:.1f}, {corner_points[1][1]:.1f})")
                print(f"右下角 (C3): ({corner_points[2][0]:.1f}, {corner_points[2][1]:.1f})")
                print(f"左下角 (C4): ({corner_points[3][0]:.1f}, {corner_points[3][1]:.1f})")

                # ser.write(int(corner_points[0][0]).encode('utf-8'))
                # ser.write(int(corner_points[0][1]).encode('utf-8'))
                # ser.write(int(corner_points[1][0]).encode('utf-8'))
                # ser.write(int(corner_points[1][1]).encode('utf-8'))
                # ser.write(int(corner_points[2][0]).encode('utf-8'))
                # ser.write(int(corner_points[2][1]).encode('utf-8'))
                # ser.write(int(corner_points[3][0]).encode('utf-8'))
                # ser.write(int(corner_points[3][1]).encode('utf-8'))
                    
                if red_laser:
                    print(f"\n红色激光点位置: ({red_laser[0]}, {red_laser[1]})")

                    # ser.write(int(red_laser[0]).encode('utf-8'))
                    # ser.write(int(red_laser[1]).encode('utf-8'))
                else:
                    print("\n未检测到红色激光点")
                        
                if green_laser:
                    print(f"绿色激光点位置: ({green_laser[0]}, {green_laser[1]})")

                    # ser.write(int(green_laser[0]).encode('utf-8'))
                    # ser.write(int(green_laser[1]).encode('utf-8'))
                else:
                    print("未检测到绿色激光点")
                    
            except Exception as e:
                print(f"错误: {str(e)}")

            cv2.putText(frame, "fps: " + str(fps), (0, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.65, (0, 255, 0), 1)
            cv2.imshow('result', frame)
            pre_time = time.perf_counter_ns()
            if cv2.waitKey(1) & 0xFF == 27:
                break
    vc.release()
    cv2.destroyAllWindows()