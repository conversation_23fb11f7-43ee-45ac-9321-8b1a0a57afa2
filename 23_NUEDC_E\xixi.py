# 250722

# 纯视觉，激光点识别不太行

import time
import cv2
import numpy as np

def find_black_rectangle(img):
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    lower_black = np.array([0, 0, 0])
    upper_black = np.array([180, 255, 40])
    mask = cv2.inRange(hsv, lower_black, upper_black)
    
    # 形态学操作增强黑色区域
    kernel = np.ones((5, 5), np.uint8)
    mask = cv2.dilate(mask, kernel, iterations=1)
    
    # 寻找轮廓
    contours, _ = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    
    # 寻找最大的四边形轮廓（假设黑色矩形是最大四边形）
    max_area = 0
    rectangle_contour = None
    
    for contour in contours:
        # 多边形逼近
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        
        # 寻找四边形
        if len(approx) == 4:
            area = cv2.contourArea(approx)
            if area > max_area:
                max_area = area
                rectangle_contour = approx
    
    if rectangle_contour is None:
        return None, None
    
    # 获取外角点（矩形轮廓的四个角点）
    outer_corners = rectangle_contour.reshape(-1, 2)
    
    # 计算内角点：通过缩小轮廓获取内矩形
    center = np.mean(outer_corners, axis=0)
    scaled_inner_corners = 0.9 * (outer_corners - center) + center
    inner_corners = scaled_inner_corners.astype(np.int32)
    
    return outer_corners, inner_corners

def find_red_laser(img):
    """识别红色激光点并返回坐标 - 使用多阈值处理优化方法"""
    # 方法1：传统HSV红色范围检测
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    lower_red1 = np.array([0, 100, 100])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([170, 100, 100])
    upper_red2 = np.array([180, 255, 255])
    
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask_hsv = cv2.bitwise_or(mask1, mask2)
    
    # 方法2：亮度和饱和度组合检测
    h, s, v = cv2.split(hsv)
    _, bright_mask = cv2.threshold(v, 200, 255, cv2.THRESH_BINARY)  # 高亮度区域
    _, sat_mask = cv2.threshold(s, 100, 255, cv2.THRESH_BINARY)    # 高饱和度区域
    
    # 组合亮度和饱和度掩码
    mask_bs = cv2.bitwise_and(bright_mask, sat_mask)
    
    # 方法3：LAB颜色空间处理（更好的红色隔离）
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    # 增强红色通道 (a通道在LAB空间中对应红-绿轴)
    a_norm = cv2.normalize(a, None, 0, 255, cv2.NORM_MINMAX)
    _, a_mask = cv2.threshold(a_norm, 150, 255, cv2.THRESH_BINARY)
    
    # 组合三个方法的结果
    final_mask = cv2.bitwise_or(mask_hsv, mask_bs)
    final_mask = cv2.bitwise_or(final_mask, a_mask)
    
    # 形态学处理减少噪声
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
    final_mask = cv2.dilate(final_mask, kernel, iterations=1)
    
    # 寻找轮廓
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 筛选最佳激光点轮廓
    laser_contour = None
    max_circularity = 0
    
    for contour in contours:
        # 面积筛选 - 移除过小或过大的噪声
        area = cv2.contourArea(contour)
        if area > 1000:
            continue
            
        # 计算圆形度
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue
            
        circularity = 4 * np.pi * area / (perimeter ** 2)
        
        # 更新最圆的轮廓
        if circularity > max_circularity:
            max_circularity = circularity
            laser_contour = contour
    
    if laser_contour is None:
        return None
    
    # 计算质心（激光点中心）
    M = cv2.moments(laser_contour)
    if M["m00"] != 0:
        cx = int(M["m10"] / M["m00"])
        cy = int(M["m01"] / M["m00"])
        
        # 验证位置是否合理
        height, width = img.shape[:2]
        if 0 <= cx < width and 0 <= cy < height:
            return (cx, cy)
    
    return None

def detect_features(img):
    """主函数：检测图片中的特征"""
    # 读取图像
    if img is None:
        print(f"无法读取图像: {img}")
        return
    
    # 创建结果图像副本
    result_img = img.copy()
    
    # 检测黑色矩形
    outer_corners, inner_corners = find_black_rectangle(img)
    
    if outer_corners is not None:
        print(f"黑色矩形外角点坐标: {outer_corners.tolist()}")
        
        # 在图像上标记外角点
        for i, (x, y) in enumerate(outer_corners):
            cv2.circle(result_img, (x, y), 8, (0, 255, 0), -1)  # 绿色点表示外角
            cv2.putText(result_img, f"O{i+1}", (x-20, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    if inner_corners is not None:
        print(f"黑色矩形内角点坐标: {inner_corners.tolist()}")
        
        # 在图像上标记内角点
        for i, (x, y) in enumerate(inner_corners):
            cv2.circle(result_img, (x, y), 6, (255, 0, 0), -1)  # 蓝色点表示内角
            cv2.putText(result_img, f"I{i+1}", (x-15, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
    
    # 检测红色激光点
    laser_point = find_red_laser(img)
    
    if laser_point is not None:
        x, y = laser_point
        print(f"红色激光点坐标: ({x}, {y})")
        
        # 在图像上标记激光点
        cv2.circle(result_img, (x, y), 10, (0, 0, 255), -1)  # 红色点表示激光点
        cv2.putText(result_img, "LASER", (x-40, y-20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
    return result_img

pre_time = time.perf_counter_ns()

# 初始化折线图相关变量
fps_history = []
timestamps = []
graph_width = 800
graph_height = 300
graph_margin = 50  # 图像边缘留白
max_history = 1000  # 最多保存的FPS数量
line_color = (255, 255, 0)  # 绿色折线

# 创建折线图画布（黑色背景）
graph_image = np.zeros((graph_height, graph_width, 3), dtype=np.uint8)

vc = cv2.VideoCapture(0)

vc.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减小缓冲区
# vc.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('M','J','P','G')) # 使用MJPEG

vc.set(cv2.CAP_PROP_FRAME_WIDTH, 2560)
vc.set(cv2.CAP_PROP_FRAME_HEIGHT, 1440)

# vc.set(cv2.CAP_PROP_FPS, 30)

# print(vc.get(cv2.CAP_PROP_FPS))

# vc.set(cv2.CAP_PROP_AUTO_EXPOSURE, 0.25)  # 0.25表示手动曝光模式
# exposure_time = 10  # 示例值（30ms），实际值需根据摄像头调整
# vc.set(cv2.CAP_PROP_EXPOSURE, exposure_time / 1000)  # OpenCV需要秒为单位

# 检查打开是否正确
if vc.isOpened():
    open, frame = vc.read()
else:
    open = False
    print('Failed to open camera')

# 创建两个窗口
cv2.namedWindow('Video Feed', cv2.WINDOW_NORMAL)
cv2.namedWindow('FPS Monitor', cv2.WINDOW_NORMAL)
cv2.resizeWindow('FPS Monitor', graph_width, graph_height)

while open:
    ret, frame = vc.read()
    real_time = time.perf_counter_ns()
    if frame is None:
        break
    if ret is True:
        # 计算当前FPS
        current_fps = 1000000000 / (real_time - pre_time)
        
        # 更新FPS历史数据
        fps_history.append(current_fps)
        timestamps.append(time.perf_counter_ns())
        
        # 移除10秒前的旧数据
        while fps_history and (timestamps[-1] - timestamps[0]) > 10000000000:  # 10秒
            fps_history.pop(0)
            timestamps.pop(0)
        
        # 在视频上显示当前FPS
        frame = detect_features(frame)
        cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.65, (0, 255, 0), 2)
        cv2.imshow('Video Feed', frame)
        
        # 准备FPS折线图
        graph_image.fill(0)  # 清空画布
        
        if len(fps_history) > 1:
            # 计算纵轴范围（FPS范围）
            min_fps = max(0, min(fps_history) * 0.9)
            max_fps = max(fps_history) * 1.1
            
            # 绘制坐标轴
            cv2.line(graph_image, 
                    (graph_margin, graph_margin), 
                    (graph_margin, graph_height - graph_margin), 
                    (128, 128, 128), 1)
            cv2.line(graph_image, 
                    (graph_margin, graph_height - graph_margin), 
                    (graph_width - graph_margin, graph_height - graph_margin), 
                    (128, 128, 128), 1)
            
            # 绘制参考线
            for i in range(0, int(max_fps) + 20, 10):
                y_pos = graph_height - graph_margin - int(
                    (i - min_fps) / (max_fps - min_fps) * (graph_height - 2 * graph_margin))
                if y_pos > graph_margin:
                    cv2.line(graph_image, 
                            (graph_margin, y_pos), 
                            (graph_width - graph_margin, y_pos), 
                            (50, 50, 50), 1)
                    cv2.putText(graph_image, str(i), 
                               (5, y_pos + 5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, 
                               (200, 200, 200), 1)
            
            # 绘制折线
            points = []
            for i, fps in enumerate(fps_history):
                # 计算点坐标
                x = int(graph_margin + i * (graph_width - 2 * graph_margin) / len(fps_history))
                y = graph_height - graph_margin - int(
                    (fps - min_fps) / (max_fps - min_fps) * (graph_height - 2 * graph_margin))
                points.append((x, y))
            
            # 连接所有点形成折线
            for i in range(1, len(points)):
                cv2.line(graph_image, points[i-1], points[i], line_color, 2)
            
            # 显示平均FPS
            avg_fps = sum(fps_history) / len(fps_history)
            cv2.putText(graph_image, f"Avg FPS: {avg_fps:.1f}", 
                       (graph_width - 200, 25), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.6, (0, 255, 255), 1)
        
        # 显示当前FPS
        cv2.putText(graph_image, f"Current FPS: {current_fps:.1f}", 
                   (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)
        
        # 显示折线图
        cv2.imshow('FPS Monitor', graph_image)
        
        # 更新时间戳
        pre_time = time.perf_counter_ns()
        
        # 检查退出条件
        if cv2.waitKey(1) & 0xFF == 27:  # ESC键退出
            break

# 释放资源
vc.release()
cv2.destroyAllWindows()
