#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速激光点标注工具
简化版本，专注于快速标注
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime

def quick_annotate_video():
    """快速标注视频中的激光点"""
    cap = cv2.VideoCapture('output.mp4')
    annotations = []
    frame_count = 0
    
    print("快速标注工具")
    print("=" * 40)
    print("操作说明:")
    print("- 鼠标左键: 点击激光点位置")
    print("- 'n'键: 跳过当前帧(无激光点)")
    print("- 'q'键: 退出并保存")
    print("- 's'键: 保存当前进度")
    print("=" * 40)
    
    current_annotation = None
    
    def mouse_callback(event, x, y, flags, param):
        nonlocal current_annotation
        if event == cv2.EVENT_LBUTTONDOWN:
            # 转换为ROI坐标
            roi_x = x - 260 if x >= 260 and x <= 470 else None
            roi_y = y - 65 if y >= 65 and y <= 410 else None
            
            current_annotation = {
                'frame': frame_count,
                'x': x,
                'y': y,
                'roi_x': roi_x,
                'roi_y': roi_y,
                'timestamp': datetime.now().isoformat()
            }
            print(f"标注帧{frame_count}: ({x}, {y}) ROI:({roi_x}, {roi_y})")
    
    cv2.namedWindow('Quick Annotation')
    cv2.setMouseCallback('Quick Annotation', mouse_callback)
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame = cv2.resize(frame, (640, 480))
        display_frame = frame.copy()
        
        # 绘制ROI区域
        cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
        cv2.putText(display_frame, "ROI", (265, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 显示当前标注
        if current_annotation and current_annotation['frame'] == frame_count:
            x, y = current_annotation['x'], current_annotation['y']
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
        
        # 显示帧信息
        cv2.putText(display_frame, f"Frame: {frame_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('Quick Annotation', display_frame)
        
        key = cv2.waitKey(0) & 0xFF
        
        if key == ord('q'):
            break
        elif key == ord('n'):
            # 跳过当前帧
            print(f"跳过帧{frame_count}")
            current_annotation = None
        elif key == ord('s'):
            # 保存当前进度
            if current_annotation:
                annotations.append(current_annotation)
            save_annotations(annotations)
            continue
        
        # 保存当前标注并进入下一帧
        if current_annotation:
            annotations.append(current_annotation)
        
        current_annotation = None
        frame_count += 1
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 保存最终标注
    save_annotations(annotations)
    return annotations

def save_annotations(annotations):
    """保存标注数据"""
    if not os.path.exists("laser_annotations"):
        os.makedirs("laser_annotations")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"laser_annotations/quick_annotations_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(annotations, f, indent=2, ensure_ascii=False)
    
    print(f"标注数据已保存到: {filename}")
    print(f"总共标注了 {len(annotations)} 帧")

def compare_with_detection():
    """比较标注数据与检测结果"""
    # 加载最新的标注数据
    annotation_files = [f for f in os.listdir("laser_annotations") if f.endswith(".json")]
    if not annotation_files:
        print("未找到标注文件")
        return
    
    latest_file = os.path.join("laser_annotations", sorted(annotation_files)[-1])
    
    with open(latest_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    print(f"加载标注文件: {latest_file}")
    print(f"标注数据: {len(annotations)} 帧")
    
    # 导入检测算法
    try:
        from final_enhanced_detector import detect_laser_point_final
        detect_func = detect_laser_point_final
        print("使用 final_enhanced_detector")
    except:
        try:
            from precision_laser_detector import detect_laser_point_precision
            detect_func = detect_laser_point_precision
            print("使用 precision_laser_detector")
        except:
            print("未找到检测算法")
            return
    
    cap = cv2.VideoCapture('output.mp4')
    comparison_results = []
    
    for ann in annotations:
        frame_num = ann['frame']
        true_x, true_y = ann['x'], ann['y']
        true_roi_x, true_roi_y = ann.get('roi_x'), ann.get('roi_y')
        
        # 加载对应帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            frame = cv2.resize(frame, (640, 480))
            roi_frame = frame[65:410, 260:470, :]
            
            # 检测激光点
            detected = detect_func(roi_frame)
            
            if detected and true_roi_x is not None and true_roi_y is not None:
                # 计算误差
                error_x = abs(detected[0] - true_roi_x)
                error_y = abs(detected[1] - true_roi_y)
                distance_error = np.sqrt(error_x**2 + error_y**2)
                
                result = {
                    'frame': frame_num,
                    'true_position': (true_roi_x, true_roi_y),
                    'detected_position': detected,
                    'error_x': error_x,
                    'error_y': error_y,
                    'distance_error': distance_error,
                    'detection_success': True
                }
            else:
                result = {
                    'frame': frame_num,
                    'true_position': (true_roi_x, true_roi_y) if true_roi_x is not None else None,
                    'detected_position': detected,
                    'detection_success': detected is not None,
                    'ground_truth_available': true_roi_x is not None
                }
            
            comparison_results.append(result)
    
    cap.release()
    
    # 分析结果
    analyze_comparison_results(comparison_results)
    
    # 保存比较结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"laser_annotations/comparison_results_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_results, f, indent=2)
    
    print(f"比较结果已保存到: {result_file}")

def analyze_comparison_results(results):
    """分析比较结果"""
    print("\n" + "=" * 50)
    print("检测算法性能分析")
    print("=" * 50)
    
    successful_detections = [r for r in results if r.get('detection_success', False) and r.get('distance_error') is not None]
    
    if successful_detections:
        errors = [r['distance_error'] for r in successful_detections]
        
        print(f"成功检测帧数: {len(successful_detections)}")
        print(f"总标注帧数: {len(results)}")
        print(f"检测成功率: {len(successful_detections)/len(results)*100:.1f}%")
        print(f"平均误差: {np.mean(errors):.2f} 像素")
        print(f"最大误差: {np.max(errors):.2f} 像素")
        print(f"最小误差: {np.min(errors):.2f} 像素")
        print(f"误差标准差: {np.std(errors):.2f} 像素")
        
        # 误差分布
        small_errors = len([e for e in errors if e <= 5])
        medium_errors = len([e for e in errors if 5 < e <= 10])
        large_errors = len([e for e in errors if e > 10])
        
        print(f"\n误差分布:")
        print(f"  ≤5像素: {small_errors} ({small_errors/len(errors)*100:.1f}%)")
        print(f"  5-10像素: {medium_errors} ({medium_errors/len(errors)*100:.1f}%)")
        print(f"  >10像素: {large_errors} ({large_errors/len(errors)*100:.1f}%)")
        
        # 找出误差最大的帧
        max_error_result = max(successful_detections, key=lambda x: x['distance_error'])
        print(f"\n最大误差帧: {max_error_result['frame']}")
        print(f"  真实位置: {max_error_result['true_position']}")
        print(f"  检测位置: {max_error_result['detected_position']}")
        print(f"  误差: {max_error_result['distance_error']:.2f} 像素")
    
    else:
        print("没有成功的检测结果用于分析")

def main():
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "compare":
            compare_with_detection()
        elif sys.argv[1] == "annotate":
            quick_annotate_video()
    else:
        print("使用方法:")
        print("  python quick_annotation_tool.py annotate  # 开始标注")
        print("  python quick_annotation_tool.py compare   # 比较检测结果")
        
        choice = input("选择操作 (1-标注, 2-比较): ")
        if choice == "1":
            quick_annotate_video()
        elif choice == "2":
            compare_with_detection()

if __name__ == "__main__":
    main()
