#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的激光点检测器 - 专为Jetson Nano等嵌入式设备优化
支持白纸和黑线背景下的红色激光点检测
"""

import cv2
import numpy as np
import time

def detect_laser_point(image):
    """
    检测图像中的红色激光点位置
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    # 获取图像尺寸
    height, width = image.shape[:2]
    
    # 方法1: 基于亮度的快速检测 (适用于白纸背景)
    laser_point = _detect_by_brightness(image)
    if laser_point is not None:
        return laser_point
    
    # 方法2: 多颜色空间组合检测 (适用于复杂背景)
    laser_point = _detect_by_color_spaces(image)
    if laser_point is not None:
        return laser_point
    
    # 方法3: 边缘增强检测 (适用于黑线背景)
    laser_point = _detect_by_edge_enhancement(image)
    if laser_point is not None:
        return laser_point
    
    return None

def _detect_by_brightness(image):
    """基于亮度的快速检测方法"""
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 查找最亮的区域
    _, thresh = cv2.threshold(gray, 240, 255, cv2.THRESH_BINARY)
    
    # 形态学操作去除噪声
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 筛选最佳候选
    best_contour = None
    best_score = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area < 5 or area > 500:  # 面积筛选
            continue
        
        # 计算圆形度
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue
        
        circularity = 4 * np.pi * area / (perimeter ** 2)
        
        # 综合评分：圆形度 + 面积权重
        score = circularity * min(area / 50, 1.0)
        
        if score > best_score and circularity > 0.3:
            best_score = score
            best_contour = contour
    
    if best_contour is not None:
        # 计算质心
        M = cv2.moments(best_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def _detect_by_color_spaces(image):
    """多颜色空间组合检测方法"""
    # HSV颜色空间检测
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 红色范围1 (0-10度)
    lower_red1 = np.array([0, 120, 150])
    upper_red1 = np.array([10, 255, 255])
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    
    # 红色范围2 (170-180度)
    lower_red2 = np.array([170, 120, 150])
    upper_red2 = np.array([180, 255, 255])
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    
    # 合并HSV掩码
    hsv_mask = cv2.bitwise_or(mask1, mask2)
    
    # LAB颜色空间检测 (更好的红色分离)
    lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(lab)
    
    # A通道增强红色
    a_enhanced = cv2.normalize(a, None, 0, 255, cv2.NORM_MINMAX)
    _, a_mask = cv2.threshold(a_enhanced, 140, 255, cv2.THRESH_BINARY)
    
    # 亮度掩码
    _, l_mask = cv2.threshold(l, 120, 255, cv2.THRESH_BINARY)
    
    # 组合所有掩码
    combined_mask = cv2.bitwise_and(hsv_mask, a_mask)
    combined_mask = cv2.bitwise_and(combined_mask, l_mask)
    
    # 形态学处理
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
    combined_mask = cv2.dilate(combined_mask, kernel, iterations=1)
    
    return _find_best_laser_candidate(combined_mask)

def _detect_by_edge_enhancement(image):
    """边缘增强检测方法 (适用于黑线背景)"""
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 拉普拉斯边缘检测
    laplacian = cv2.Laplacian(blurred, cv2.CV_64F)
    laplacian = np.uint8(np.absolute(laplacian))
    
    # 阈值处理
    _, thresh = cv2.threshold(laplacian, 30, 255, cv2.THRESH_BINARY)
    
    # 与亮度掩码结合
    _, bright_mask = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)
    
    # 组合边缘和亮度信息
    combined = cv2.bitwise_and(thresh, bright_mask)
    
    # 形态学处理
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
    
    return _find_best_laser_candidate(combined)

def _find_best_laser_candidate(mask):
    """从掩码中找到最佳激光点候选"""
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    best_contour = None
    best_score = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        # 面积筛选
        if area < 3 or area > 800:
            continue
        
        # 计算边界框
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = float(w) / h
        
        # 计算圆形度
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue
        
        circularity = 4 * np.pi * area / (perimeter ** 2)
        
        # 计算紧密度 (轮廓面积/边界框面积)
        bbox_area = w * h
        extent = float(area) / bbox_area if bbox_area > 0 else 0
        
        # 综合评分
        # 优先考虑：圆形度、合适的长宽比、适中的面积
        score = (circularity * 0.4 + 
                extent * 0.3 + 
                (1.0 - abs(aspect_ratio - 1.0)) * 0.2 +
                min(area / 100, 1.0) * 0.1)
        
        if score > best_score and circularity > 0.2:
            best_score = score
            best_contour = contour
    
    if best_contour is not None:
        # 计算质心
        M = cv2.moments(best_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def visualize_detection(image, laser_point):
    """
    可视化检测结果
    
    参数:
        image: 原始图像
        laser_point: 检测到的激光点坐标
    
    返回:
        带有标记的图像
    """
    result = image.copy()
    
    if laser_point is not None:
        x, y = laser_point
        # 绘制十字标记
        cv2.drawMarker(result, (x, y), (0, 255, 0), cv2.MARKER_CROSS, 20, 2)
        # 绘制圆圈
        cv2.circle(result, (x, y), 15, (0, 0, 255), 2)
        # 添加文本
        cv2.putText(result, f"Laser({x},{y})", (x-50, y-25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    return result

# 测试函数
def test_with_video(video_path=None):
    """
    使用视频测试激光点检测
    
    参数:
        video_path: 视频文件路径，None表示使用摄像头
    """
    if video_path is None:
        cap = cv2.VideoCapture(0)
    else:
        cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print("无法打开视频源")
        return
    
    fps_counter = 0
    start_time = time.time()
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 检测激光点
        laser_point = detect_laser_point(frame)
        
        # 可视化结果
        result = visualize_detection(frame, laser_point)
        
        # 计算FPS
        fps_counter += 1
        elapsed = time.time() - start_time
        if elapsed > 1.0:
            fps = fps_counter / elapsed
            fps_counter = 0
            start_time = time.time()
            
            # 显示FPS
            cv2.putText(result, f"FPS: {fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        
        cv2.imshow('Laser Detection', result)
        
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    # 测试代码
    test_with_video("output.mp4")  # 使用指定视频文件测试
    # test_with_video()  # 使用摄像头测试
