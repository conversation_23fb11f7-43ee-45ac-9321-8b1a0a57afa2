# 250316

import cv2
from sympy.testing.pytest import warns

img = cv2.imread("bilicv/img01.jpg")

top_size , bottom_size, left_size, right_size = 100, 100, 100, 100

replicate = cv2.copyMakeBorder(img, top_size, bottom_size, left_size, right_size, cv2.BORDER_REPLICATE)
reflect = cv2.copyMakeBorder(img, top_size, bottom_size, left_size, right_size, cv2.BORDER_REFLECT)
reflect101 = cv2.copyMakeBorder(img, top_size, bottom_size, left_size, right_size, cv2.BORDER_REFLECT_101)
wrap = cv2.copyMakeBorder(img, top_size, bottom_size, left_size, right_size, cv2.BORDER_WRAP)
constant = cv2.copyMakeBorder(img, top_size, bottom_size, left_size, right_size, cv2.BORDER_CONSTANT, value=0)

cv2.imshow("original", img)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("replicate", replicate)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("reflect", reflect)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("reflect101", reflect101)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("wrap", wrap)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("constant", constant)
cv2.waitKey(0)
cv2.destroyAllWindows()
