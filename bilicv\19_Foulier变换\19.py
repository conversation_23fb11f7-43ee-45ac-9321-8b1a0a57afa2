# 250318

import cv2
import  numpy as np
import matplotlib.pyplot as plt

img = cv2.imread("bilicv/img03.jpg", cv2.IMREAD_GRAYSCALE)

img_float32 = np.float32(img)

dft = cv2.dft(img_float32, flags = cv2.DFT_COMPLEX_OUTPUT)
dft_shift = np.fft.fftshift(dft)

rows, cols = img.shape
crow, ccol = int(rows/2), int(cols/2)       # 中心位置

# 低通滤波
mask0 = np.zeros((rows, cols, 2), np.uint8)
mask0[crow-30:crow+30, ccol-30:ccol+30] = 1

# 高通滤波
mask1 = np.ones((rows, cols, 2), np.uint8)
mask1[crow-30:crow+30, ccol-30:ccol+30] = 0

# IDFT_Low
fshift0 = dft_shift*mask0
f_ishift0 = np.fft.ifftshift(fshift0)
img_back0 = cv2.idft(f_ishift0)
img_back0 = cv2.magnitude(img_back0[:,:,0], img_back0[:, :, 1])

plt.subplots(1), plt.imshow(img_back0, cmap = 'gray')

plt.show()

# IDFT_High
fshift1 = dft_shift*mask1
f_ishift1 = np.fft.ifftshift(fshift1)
img_back1 = cv2.idft(f_ishift1)
img_back1 = cv2.magnitude(img_back1[:,:,0], img_back1[:, :, 1])

plt.subplots(1), plt.imshow(img_back1, cmap = 'gray')

plt.show()
