#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的激光点检测器 - 基于视频分析结果优化
专为Jetson Nano等嵌入式设备设计，性能优化
"""

import cv2
import numpy as np
import time

def detect_laser_point(image):
    """
    检测图像中的红色激光点位置 - 改进版
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    height, width = image.shape[:2]
    
    # 性能优化：如果图像太大，先缩小处理
    scale_factor = 1.0
    if width > 1280:
        scale_factor = 1280.0 / width
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        image = cv2.resize(image, (new_width, new_height))
    
    # 方法1: 优化的亮度检测（最有效的方法）
    laser_point = _detect_bright_spots(image)
    if laser_point is not None:
        if scale_factor != 1.0:
            x, y = laser_point
            x = int(x / scale_factor)
            y = int(y / scale_factor)
            return (x, y)
        return laser_point
    
    # 方法2: 红色检测（备用方法）
    laser_point = _detect_red_laser(image)
    if laser_point is not None:
        if scale_factor != 1.0:
            x, y = laser_point
            x = int(x / scale_factor)
            y = int(y / scale_factor)
            return (x, y)
        return laser_point
    
    # 方法3: 边缘增强检测（适用于黑线背景）
    laser_point = _detect_edge_enhanced(image)
    if laser_point is not None:
        if scale_factor != 1.0:
            x, y = laser_point
            x = int(x / scale_factor)
            y = int(y / scale_factor)
            return (x, y)
        return laser_point
    
    return None

def _detect_bright_spots(image):
    """检测最亮的点 - 基于视频分析优化"""
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 找到图像中的最大亮度值
    max_brightness = np.max(gray)
    
    # 如果最大亮度不够高，说明可能没有激光点
    if max_brightness < 200:
        return None
    
    # 使用自适应阈值，基于最大亮度
    threshold = max(240, max_brightness - 15)
    _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
    
    # 轻微的形态学操作去除噪声
    kernel = np.ones((3, 3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 筛选最佳候选点
    best_candidate = None
    best_score = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        # 面积筛选 - 激光点通常很小
        if area < 2 or area > 200:
            continue
        
        # 计算轮廓的紧密度
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = float(w) / h if h > 0 else 0
        extent = float(area) / (w * h) if w * h > 0 else 0
        
        # 计算圆形度
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue
        circularity = 4 * np.pi * area / (perimeter ** 2)
        
        # 综合评分：优先考虑圆形、紧密、适中大小的区域
        score = (circularity * 0.4 + 
                extent * 0.3 + 
                (1.0 - abs(aspect_ratio - 1.0)) * 0.2 +
                min(area / 20, 1.0) * 0.1)
        
        if score > best_score and circularity > 0.3:
            best_score = score
            best_candidate = contour
    
    if best_candidate is not None:
        # 计算质心
        M = cv2.moments(best_candidate)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def _detect_red_laser(image):
    """红色激光检测 - 优化版"""
    # 转换为HSV颜色空间
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 优化的红色范围 - 基于实际测试调整
    lower_red1 = np.array([0, 80, 120])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([165, 80, 120])
    upper_red2 = np.array([180, 255, 255])
    
    # 创建掩码
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 选择最大的轮廓
    largest_contour = max(contours, key=cv2.contourArea)
    area = cv2.contourArea(largest_contour)
    
    if 3 <= area <= 300:
        M = cv2.moments(largest_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def _detect_edge_enhanced(image):
    """边缘增强检测 - 适用于黑线背景"""
    # 转换为灰度
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 使用Sobel算子检测边缘
    sobelx = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
    sobel = np.sqrt(sobelx**2 + sobely**2)
    sobel = np.uint8(sobel)
    
    # 阈值处理
    _, edge_mask = cv2.threshold(sobel, 30, 255, cv2.THRESH_BINARY)
    
    # 亮度掩码
    _, bright_mask = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)
    
    # 组合边缘和亮度
    combined = cv2.bitwise_and(edge_mask, bright_mask)
    
    # 形态学闭运算
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(combined, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 选择最圆的轮廓
    best_contour = None
    best_circularity = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if 3 <= area <= 100:
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter ** 2)
                if circularity > best_circularity:
                    best_circularity = circularity
                    best_contour = contour
    
    if best_contour is not None and best_circularity > 0.3:
        M = cv2.moments(best_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

class LaserTracker:
    """激光点跟踪器 - 提供时间滤波和稳定性"""
    
    def __init__(self, history_size=5, confidence_threshold=3):
        self.history = []
        self.history_size = history_size
        self.confidence_threshold = confidence_threshold
        self.last_position = None
        
    def update(self, detection):
        """更新检测结果"""
        if detection is not None:
            self.history.append(detection)
            if len(self.history) > self.history_size:
                self.history.pop(0)
            
            # 如果有足够的连续检测，计算平均位置
            if len(self.history) >= self.confidence_threshold:
                avg_x = sum(p[0] for p in self.history) // len(self.history)
                avg_y = sum(p[1] for p in self.history) // len(self.history)
                self.last_position = (avg_x, avg_y)
                return self.last_position
            else:
                return detection
        else:
            # 如果当前帧没有检测到，但历史记录中有，可能是暂时遮挡
            if len(self.history) > 0:
                # 逐渐减少历史记录的权重
                self.history = self.history[1:]
            
            return self.last_position if len(self.history) >= 2 else None
    
    def get_confidence(self):
        """获取当前检测的置信度"""
        return len(self.history) / self.history_size

def test_improved_detector():
    """测试改进的检测器"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    tracker = LaserTracker()
    frame_count = 0
    detection_count = 0
    
    # 性能计时
    total_time = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 计时开始
        start_time = time.time()
        
        # 检测激光点
        laser_point = detect_laser_point(frame)
        
        # 使用跟踪器更新
        tracked_point = tracker.update(laser_point)
        
        # 计时结束
        end_time = time.time()
        total_time += (end_time - start_time)
        
        if tracked_point:
            detection_count += 1
            x, y = tracked_point
            confidence = tracker.get_confidence()
            
            if frame_count <= 20 or frame_count % 50 == 0:  # 只打印部分结果
                print(f"Frame {frame_count}: 激光点 ({x}, {y}), 置信度: {confidence:.2f}")
            
            # 在图像上标记
            cv2.circle(frame, (x, y), 10, (0, 255, 0), 2)
            cv2.putText(frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            cv2.putText(frame, f"Conf: {confidence:.2f}", (x+15, y+25), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
        
        # 显示FPS
        if frame_count > 1:
            avg_fps = frame_count / total_time
            cv2.putText(frame, f"FPS: {avg_fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        
        # 显示结果（每10帧显示一次以提高性能）
        if frame_count % 10 == 0:
            cv2.imshow('Improved Laser Detection', frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    
    print(f"\n=== 检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {total_time/frame_count*1000:.2f}ms/帧")

if __name__ == "__main__":
    print("开始测试改进的激光点检测器...")
    test_improved_detector()
