#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
激光点标注工具
用于标注正确的激光点位置，生成训练数据
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime

class LaserAnnotationTool:
    def __init__(self, video_path="output.mp4"):
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        self.annotations = []
        self.current_frame = 0
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.current_annotation = None
        self.roi_offset = (260, 65)  # ROI偏移量
        
        # 创建输出目录
        self.output_dir = "laser_annotations"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
        print("激光点标注工具")
        print("=" * 50)
        print("操作说明:")
        print("- 鼠标左键: 标注激光点位置")
        print("- 鼠标右键: 删除当前标注")
        print("- 空格键: 下一帧")
        print("- 'b'键: 上一帧")
        print("- 'j'键: 跳转到指定帧")
        print("- 's'键: 保存标注数据")
        print("- 'q'键: 退出")
        print("- 'n'键: 标记为无激光点")
        print("=" * 50)
    
    def mouse_callback(self, event, x, y, flags, param):
        """鼠标回调函数"""
        if event == cv2.EVENT_LBUTTONDOWN:
            # 左键点击标注激光点
            self.current_annotation = {
                'frame': self.current_frame,
                'x': x,
                'y': y,
                'roi_x': x - self.roi_offset[0] if x >= self.roi_offset[0] else None,
                'roi_y': y - self.roi_offset[1] if y >= self.roi_offset[1] else None,
                'timestamp': datetime.now().isoformat(),
                'has_laser': True
            }
            print(f"标注激光点: 帧{self.current_frame}, 位置({x}, {y})")
            self.update_display()
            
        elif event == cv2.EVENT_RBUTTONDOWN:
            # 右键删除标注
            self.current_annotation = None
            print(f"删除标注: 帧{self.current_frame}")
            self.update_display()
    
    def load_frame(self, frame_number):
        """加载指定帧"""
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = self.cap.read()
        if ret:
            self.current_frame = frame_number
            frame = cv2.resize(frame, (640, 480))
            return frame
        return None
    
    def update_display(self):
        """更新显示"""
        frame = self.load_frame(self.current_frame)
        if frame is None:
            return
        
        display_frame = frame.copy()
        
        # 绘制ROI区域
        cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
        cv2.putText(display_frame, "ROI", (265, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 绘制当前标注
        if self.current_annotation and self.current_annotation['frame'] == self.current_frame:
            x, y = self.current_annotation['x'], self.current_annotation['y']
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(display_frame, f"Laser({x},{y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # 显示帧信息
        cv2.putText(display_frame, f"Frame: {self.current_frame}/{self.total_frames}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 显示标注状态
        if self.current_annotation and self.current_annotation['frame'] == self.current_frame:
            status = "Annotated"
            color = (0, 255, 0)
        else:
            status = "Not Annotated"
            color = (0, 0, 255)
        
        cv2.putText(display_frame, f"Status: {status}", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        cv2.imshow('Laser Annotation Tool', display_frame)
    
    def save_annotations(self):
        """保存标注数据"""
        # 保存当前标注到列表
        if self.current_annotation:
            # 检查是否已存在该帧的标注
            existing_index = None
            for i, ann in enumerate(self.annotations):
                if ann['frame'] == self.current_annotation['frame']:
                    existing_index = i
                    break
            
            if existing_index is not None:
                self.annotations[existing_index] = self.current_annotation
            else:
                self.annotations.append(self.current_annotation)
        
        # 保存到JSON文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = os.path.join(self.output_dir, f"laser_annotations_{timestamp}.json")
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.annotations, f, indent=2, ensure_ascii=False)
        
        print(f"标注数据已保存到: {json_file}")
        print(f"总共标注了 {len(self.annotations)} 帧")
        
        # 生成统计报告
        self.generate_report()
    
    def generate_report(self):
        """生成标注报告"""
        if not self.annotations:
            return
        
        report = {
            'total_annotations': len(self.annotations),
            'frames_with_laser': len([a for a in self.annotations if a.get('has_laser', True)]),
            'frames_without_laser': len([a for a in self.annotations if not a.get('has_laser', True)]),
            'roi_annotations': len([a for a in self.annotations if a.get('roi_x') is not None]),
            'annotation_summary': []
        }
        
        # 按帧号排序
        sorted_annotations = sorted(self.annotations, key=lambda x: x['frame'])
        
        for ann in sorted_annotations:
            summary = {
                'frame': ann['frame'],
                'position': f"({ann['x']}, {ann['y']})",
                'roi_position': f"({ann.get('roi_x', 'N/A')}, {ann.get('roi_y', 'N/A')})",
                'has_laser': ann.get('has_laser', True)
            }
            report['annotation_summary'].append(summary)
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.output_dir, f"annotation_report_{timestamp}.json")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"标注报告已保存到: {report_file}")
    
    def jump_to_frame(self):
        """跳转到指定帧"""
        try:
            frame_num = int(input(f"输入帧号 (0-{self.total_frames-1}): "))
            if 0 <= frame_num < self.total_frames:
                self.current_frame = frame_num
                # 检查是否有该帧的标注
                for ann in self.annotations:
                    if ann['frame'] == frame_num:
                        self.current_annotation = ann
                        break
                else:
                    self.current_annotation = None
                self.update_display()
            else:
                print("帧号超出范围!")
        except ValueError:
            print("请输入有效的帧号!")
    
    def mark_no_laser(self):
        """标记当前帧无激光点"""
        self.current_annotation = {
            'frame': self.current_frame,
            'x': None,
            'y': None,
            'roi_x': None,
            'roi_y': None,
            'timestamp': datetime.now().isoformat(),
            'has_laser': False
        }
        print(f"标记帧{self.current_frame}为无激光点")
        self.update_display()
    
    def run(self):
        """运行标注工具"""
        cv2.namedWindow('Laser Annotation Tool')
        cv2.setMouseCallback('Laser Annotation Tool', self.mouse_callback)
        
        # 加载第一帧
        self.update_display()
        
        while True:
            key = cv2.waitKey(0) & 0xFF
            
            if key == ord('q'):
                # 退出前询问是否保存
                save = input("是否保存标注数据? (y/n): ")
                if save.lower() == 'y':
                    self.save_annotations()
                break
            
            elif key == ord(' '):
                # 下一帧
                if self.current_frame < self.total_frames - 1:
                    self.current_frame += 1
                    # 检查是否有该帧的标注
                    for ann in self.annotations:
                        if ann['frame'] == self.current_frame:
                            self.current_annotation = ann
                            break
                    else:
                        self.current_annotation = None
                    self.update_display()
            
            elif key == ord('b'):
                # 上一帧
                if self.current_frame > 0:
                    self.current_frame -= 1
                    # 检查是否有该帧的标注
                    for ann in self.annotations:
                        if ann['frame'] == self.current_frame:
                            self.current_annotation = ann
                            break
                    else:
                        self.current_annotation = None
                    self.update_display()
            
            elif key == ord('j'):
                # 跳转到指定帧
                self.jump_to_frame()
            
            elif key == ord('s'):
                # 保存标注
                self.save_annotations()
            
            elif key == ord('n'):
                # 标记无激光点
                self.mark_no_laser()
        
        self.cap.release()
        cv2.destroyAllWindows()

def create_training_data_from_annotations(annotation_file):
    """从标注数据创建训练数据"""
    with open(annotation_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)

    cap = cv2.VideoCapture("output.mp4")
    training_data = []

    for ann in annotations:
        if not ann.get('has_laser', True):
            continue

        frame_num = ann['frame']
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()

        if ret:
            frame = cv2.resize(frame, (640, 480))
            roi_frame = frame[65:410, 260:470, :]

            # 提取激光点周围的特征
            roi_x, roi_y = ann.get('roi_x'), ann.get('roi_y')
            if roi_x is not None and roi_y is not None:
                features = extract_laser_features(roi_frame, (roi_x, roi_y))
                training_data.append({
                    'frame': frame_num,
                    'position': (roi_x, roi_y),
                    'features': features,
                    'is_laser': True
                })

    cap.release()

    # 保存训练数据
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    training_file = f"training_data_{timestamp}.json"

    with open(training_file, 'w', encoding='utf-8') as f:
        json.dump(training_data, f, indent=2)

    print(f"训练数据已保存到: {training_file}")
    return training_data

def extract_laser_features(image, point):
    """提取激光点特征"""
    x, y = point
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    h, w = gray.shape

    if x < 5 or y < 5 or x >= w-5 or y >= h-5:
        return None

    # 提取多种特征
    features = {}

    # 1. 亮度特征
    center_val = gray[y, x]
    local_region = gray[y-5:y+6, x-5:x+6]
    features['center_brightness'] = int(center_val)
    features['avg_brightness'] = float(np.mean(local_region))
    features['max_brightness'] = int(np.max(local_region))
    features['brightness_contrast'] = float(center_val - np.mean(local_region))

    # 2. 颜色特征
    b, g, r = image[y, x]
    features['red_component'] = int(r)
    features['green_component'] = int(g)
    features['blue_component'] = int(b)
    features['red_dominance'] = float(r - max(g, b))

    # 3. 形状特征（周围像素分布）
    angles = np.linspace(0, 2*np.pi, 8, endpoint=False)
    radius = 3
    circle_vals = []

    for angle in angles:
        px = int(x + radius * np.cos(angle))
        py = int(y + radius * np.sin(angle))
        if 0 <= px < w and 0 <= py < h:
            circle_vals.append(gray[py, px])

    if circle_vals:
        features['circle_mean'] = float(np.mean(circle_vals))
        features['circle_std'] = float(np.std(circle_vals))
        features['center_circle_contrast'] = float(center_val - np.mean(circle_vals))

    # 4. 梯度特征
    grad_x = cv2.Sobel(local_region, cv2.CV_64F, 1, 0, ksize=3)
    grad_y = cv2.Sobel(local_region, cv2.CV_64F, 0, 1, ksize=3)
    gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
    features['gradient_magnitude'] = float(gradient_magnitude[5, 5])
    features['avg_gradient'] = float(np.mean(gradient_magnitude))

    return features

def main():
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "train":
        # 训练模式：从标注数据生成训练数据
        annotation_files = [f for f in os.listdir("laser_annotations") if f.startswith("laser_annotations_") and f.endswith(".json")]
        if annotation_files:
            latest_file = os.path.join("laser_annotations", sorted(annotation_files)[-1])
            print(f"使用标注文件: {latest_file}")
            create_training_data_from_annotations(latest_file)
        else:
            print("未找到标注文件，请先运行标注工具")
    else:
        # 标注模式
        tool = LaserAnnotationTool("output.mp4")
        tool.run()

if __name__ == "__main__":
    main()
