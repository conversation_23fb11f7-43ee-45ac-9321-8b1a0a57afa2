#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应激光点检测器
基于标注数据自动调整参数，减少误识别
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime

class AdaptiveLaserDetector:
    def __init__(self, annotation_file=None):
        self.params = self._load_default_params()
        
        if annotation_file and os.path.exists(annotation_file):
            self._optimize_params_from_annotations(annotation_file)
            print(f"已从标注数据优化参数: {annotation_file}")
        else:
            print("使用默认参数")
    
    def _load_default_params(self):
        """加载默认参数"""
        return {
            'brightness_threshold_offset': 15,
            'min_brightness': 200,
            'min_contrast': 15,
            'min_area': 2,
            'max_area': 100,
            'min_circularity': 0.3,
            'red_threshold': 120,
            'red_dominance': 20,
            'score_threshold': 0.4
        }
    
    def _optimize_params_from_annotations(self, annotation_file):
        """从标注数据优化参数"""
        with open(annotation_file, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        # 分析真实激光点的特征
        features = self._extract_features_from_annotations(annotations)
        
        if features:
            # 基于特征统计调整参数
            brightness_values = [f['center_brightness'] for f in features]
            contrast_values = [f['brightness_contrast'] for f in features]
            area_values = [f.get('area', 20) for f in features]
            
            # 调整亮度阈值
            min_brightness = np.percentile(brightness_values, 10)
            self.params['min_brightness'] = max(180, min_brightness - 20)
            
            # 调整对比度阈值
            min_contrast = np.percentile(contrast_values, 10)
            self.params['min_contrast'] = max(10, min_contrast - 5)
            
            # 调整面积范围
            min_area = np.percentile(area_values, 5)
            max_area = np.percentile(area_values, 95)
            self.params['min_area'] = max(1, min_area - 2)
            self.params['max_area'] = min(200, max_area + 10)
            
            print(f"优化后参数:")
            print(f"  最小亮度: {self.params['min_brightness']}")
            print(f"  最小对比度: {self.params['min_contrast']}")
            print(f"  面积范围: {self.params['min_area']}-{self.params['max_area']}")
    
    def _extract_features_from_annotations(self, annotations):
        """从标注数据提取特征"""
        cap = cv2.VideoCapture('output.mp4')
        features = []
        
        for ann in annotations:
            if not ann.get('has_laser', True):
                continue
            
            frame_num = ann['frame']
            roi_x, roi_y = ann.get('roi_x'), ann.get('roi_y')
            
            if roi_x is None or roi_y is None:
                continue
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
            ret, frame = cap.read()
            
            if ret:
                frame = cv2.resize(frame, (640, 480))
                roi_frame = frame[65:410, 260:470, :]
                
                feature = self._extract_point_features(roi_frame, (roi_x, roi_y))
                if feature:
                    features.append(feature)
        
        cap.release()
        return features
    
    def _extract_point_features(self, image, point):
        """提取点的特征"""
        x, y = point
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        h, w = gray.shape
        
        if x < 5 or y < 5 or x >= w-5 or y >= h-5:
            return None
        
        # 亮度特征
        center_val = gray[y, x]
        local_region = gray[y-3:y+4, x-3:x+4]
        avg_surrounding = np.mean(local_region)
        contrast = center_val - avg_surrounding
        
        # 颜色特征
        b, g, r = image[y, x]
        red_dominance = r - max(g, b)
        
        return {
            'center_brightness': center_val,
            'brightness_contrast': contrast,
            'red_component': r,
            'red_dominance': red_dominance,
            'area': 20  # 估计值
        }
    
    def detect_laser_point(self, image):
        """检测激光点"""
        if image is None:
            return None
        
        candidates = []
        
        # 亮度检测
        bright_candidates = self._brightness_detection(image)
        candidates.extend(bright_candidates)
        
        # 红色检测
        if len(candidates) < 2:
            red_candidates = self._red_detection(image)
            candidates.extend(red_candidates)
        
        # 选择最佳候选
        return self._select_best_candidate(candidates)
    
    def _brightness_detection(self, image):
        """亮度检测"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        max_val = np.max(gray)
        mean_val = np.mean(gray)
        
        if max_val < self.params['min_brightness']:
            return candidates
        
        # 动态阈值
        threshold = max_val - self.params['brightness_threshold_offset']
        threshold = max(threshold, self.params['min_brightness'])
        
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        
        # 形态学操作
        kernel = np.ones((3, 3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.params['min_area'] <= area <= self.params['max_area']:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    if self._verify_laser_features(gray, (cx, cy)):
                        # 计算得分
                        brightness_score = gray[cy, cx] / 255.0
                        area_score = min(area / 50.0, 1.0)
                        
                        # 圆形度
                        perimeter = cv2.arcLength(contour, True)
                        if perimeter > 0:
                            circularity = 4 * np.pi * area / (perimeter ** 2)
                        else:
                            circularity = 0
                        
                        if circularity >= self.params['min_circularity']:
                            score = brightness_score * 0.5 + area_score * 0.2 + circularity * 0.3
                            
                            if score >= self.params['score_threshold']:
                                candidates.append({
                                    'point': (cx, cy),
                                    'score': score,
                                    'method': 'brightness'
                                })
        
        return candidates
    
    def _red_detection(self, image):
        """红色检测"""
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        candidates = []
        
        # 红色范围
        lower_red1 = np.array([0, 80, 120])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([170, 80, 120])
        upper_red2 = np.array([180, 255, 255])
        
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(mask1, mask2)
        
        # 亮度掩码
        _, brightness_mask = cv2.threshold(gray, self.params['min_brightness'], 255, cv2.THRESH_BINARY)
        
        # 融合掩码
        final_mask = cv2.bitwise_and(red_mask, brightness_mask)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if self.params['min_area'] <= area <= self.params['max_area']:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    if self._verify_red_features(image, (cx, cy)):
                        score = min(area / 60.0, 1.0)
                        candidates.append({
                            'point': (cx, cy),
                            'score': score,
                            'method': 'red'
                        })
        
        return candidates
    
    def _verify_laser_features(self, gray, point):
        """验证激光点特征"""
        x, y = point
        h, w = gray.shape
        
        if x < 4 or y < 4 or x >= w-4 or y >= h-4:
            return False
        
        center_val = gray[y, x]
        if center_val < self.params['min_brightness']:
            return False
        
        # 对比度检查
        local_region = gray[y-3:y+4, x-3:x+4]
        avg_surrounding = np.mean(local_region)
        contrast = center_val - avg_surrounding
        
        if contrast < self.params['min_contrast']:
            return False
        
        return True
    
    def _verify_red_features(self, image, point):
        """验证红色特征"""
        x, y = point
        h, w = image.shape[:2]
        
        if x < 2 or y < 2 or x >= w-2 or y >= h-2:
            return False
        
        b, g, r = image[y, x]
        
        if r < self.params['red_threshold']:
            return False
        
        if r < g + self.params['red_dominance'] or r < b + self.params['red_dominance']:
            return False
        
        return True
    
    def _select_best_candidate(self, candidates):
        """选择最佳候选点"""
        if not candidates:
            return None
        
        if len(candidates) == 1:
            return candidates[0]['point']
        
        # 按得分排序
        candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)
        
        # 简单聚类选择
        best_candidate = candidates[0]
        
        # 检查是否有相近的高分候选点
        for other in candidates[1:]:
            distance = np.sqrt((best_candidate['point'][0] - other['point'][0])**2 + 
                             (best_candidate['point'][1] - other['point'][1])**2)
            if distance <= 8:
                # 如果有相近的候选点，增加置信度
                best_candidate['score'] += other['score'] * 0.2
                break
        
        return best_candidate['point']
    
    def save_params(self, filename):
        """保存参数"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.params, f, indent=2)
        print(f"参数已保存到: {filename}")
    
    def load_params(self, filename):
        """加载参数"""
        with open(filename, 'r', encoding='utf-8') as f:
            self.params = json.load(f)
        print(f"参数已从文件加载: {filename}")

def test_adaptive_detector():
    """测试自适应检测器"""
    # 查找最新的标注文件
    annotation_files = []
    if os.path.exists("laser_annotations"):
        annotation_files = [f for f in os.listdir("laser_annotations") if f.endswith(".json")]
    
    if annotation_files:
        latest_file = os.path.join("laser_annotations", sorted(annotation_files)[-1])
        detector = AdaptiveLaserDetector(latest_file)
    else:
        print("未找到标注文件，使用默认参数")
        detector = AdaptiveLaserDetector()
    
    cap = cv2.VideoCapture('output.mp4')
    frame_count = 0
    detection_count = 0
    
    print("开始测试自适应检测器...")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        frame = cv2.resize(frame, (640, 480))
        roi_frame = frame[65:410, 260:470, :]
        
        # 检测激光点
        laser_point = detector.detect_laser_point(roi_frame)
        
        if laser_point:
            detection_count += 1
            x, y = laser_point
            
            if frame_count <= 20 or frame_count % 50 == 0:
                print(f"Frame {frame_count}: 激光点 ({x}, {y})")
            
            # 转换回原图坐标并显示
            orig_x, orig_y = x + 260, y + 65
            cv2.circle(frame, (orig_x, orig_y), 8, (0, 255, 0), 2)
            cv2.circle(frame, (orig_x, orig_y), 3, (0, 0, 255), -1)
        
        # 绘制ROI
        cv2.rectangle(frame, (260, 65), (470, 410), (255, 255, 0), 2)
        
        # 显示信息
        detection_rate = detection_count / frame_count * 100
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, f"Detection: {detection_rate:.1f}%", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        if frame_count % 5 == 0:
            cv2.imshow('Adaptive Laser Detection', frame)
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    cap.release()
    cv2.destroyAllWindows()
    
    print(f"\n检测结果:")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_count/frame_count*100:.1f}%")

if __name__ == "__main__":
    test_adaptive_detector()
