# 250318

import cv2
import matplotlib.pyplot as plt
import numpy as np

img = cv2.imread("bilicv/img03.jpg", cv2.IMREAD_GRAYSCALE)

hist = cv2.calcHist([img], [0], None, [256], [0, 255])

plt.hist(img.ravel(), 256)
plt.show()

# color = ('b', 'g', 'r')
# for i, col in enumerate(color):
#     histr = cv2.calcHist([img], [i], None, [256], [0, 256])
#     plt.plot(histr, color = col)
#     plt.xlim([0,256])
#
# plt.show()

equ = cv2.equalizeHist(img)
plt.hist(equ.ravel(), 255)
plt.show()

res1 = np.hstack((img, equ))

clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(10,10))
res_clahe = clahe.apply(img)
res2 = np.hstack((img, equ, res_clahe))

cv2.imshow("a" ,res1)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("a" ,res2)
cv2.waitKey(0)
cv2.destroyAllWindows()
