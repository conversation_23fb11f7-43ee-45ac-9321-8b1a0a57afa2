#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化的激光点检测器
专为Jetson Nano等嵌入式设备设计，支持白纸和黑线背景
输入照片，输出激光点坐标的函数
"""

import cv2
import numpy as np

def detect_laser_point(image):
    """
    检测图像中的红色激光点位置
    
    参数:
        image: 输入图像 (BGR格式的numpy数组)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
        
    示例:
        import cv2
        image = cv2.imread('test.jpg')
        laser_pos = detect_laser_point(image)
        if laser_pos:
            print(f"激光点位置: {laser_pos}")
        else:
            print("未检测到激光点")
    """
    if image is None:
        return None
    
    height, width = image.shape[:2]
    
    # 性能优化：大图像先缩小处理
    scale_factor = 1.0
    if width > 1280:
        scale_factor = 1280.0 / width
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        image = cv2.resize(image, (new_width, new_height))
    
    # 方法1: 亮度检测（最有效，适用于白纸背景）
    laser_point = _detect_bright_laser(image)
    if laser_point is not None:
        return _scale_point(laser_point, scale_factor)
    
    # 方法2: 红色检测（适用于一般背景）
    laser_point = _detect_red_laser(image)
    if laser_point is not None:
        return _scale_point(laser_point, scale_factor)
    
    # 方法3: 边缘增强检测（适用于黑线背景）
    laser_point = _detect_edge_laser(image)
    if laser_point is not None:
        return _scale_point(laser_point, scale_factor)
    
    return None

def _scale_point(point, scale_factor):
    """将坐标按比例缩放回原始尺寸"""
    if scale_factor == 1.0:
        return point
    x, y = point
    return (int(x / scale_factor), int(y / scale_factor))

def _detect_bright_laser(image):
    """检测最亮的激光点 - 适用于白纸背景"""
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 找到最大亮度值
    max_brightness = np.max(gray)
    
    # 亮度阈值检查
    if max_brightness < 200:
        return None
    
    # 自适应阈值
    threshold = max(240, max_brightness - 15)
    _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
    
    # 轻微形态学操作去噪
    kernel = np.ones((3, 3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 筛选最佳候选
    best_candidate = None
    best_score = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        # 面积筛选
        if area < 2 or area > 200:
            continue
        
        # 计算形状特征
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = float(w) / h if h > 0 else 0
        extent = float(area) / (w * h) if w * h > 0 else 0
        
        # 计算圆形度
        perimeter = cv2.arcLength(contour, True)
        if perimeter == 0:
            continue
        circularity = 4 * np.pi * area / (perimeter ** 2)
        
        # 综合评分
        score = (circularity * 0.4 + 
                extent * 0.3 + 
                (1.0 - abs(aspect_ratio - 1.0)) * 0.2 +
                min(area / 20, 1.0) * 0.1)
        
        if score > best_score and circularity > 0.3:
            best_score = score
            best_candidate = contour
    
    if best_candidate is not None:
        # 计算质心
        M = cv2.moments(best_candidate)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def _detect_red_laser(image):
    """红色激光检测 - 适用于一般背景"""
    # 转换为HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 红色范围
    lower_red1 = np.array([0, 80, 120])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([165, 80, 120])
    upper_red2 = np.array([180, 255, 255])
    
    # 创建掩码
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 选择最大的轮廓
    largest_contour = max(contours, key=cv2.contourArea)
    area = cv2.contourArea(largest_contour)
    
    if 3 <= area <= 300:
        M = cv2.moments(largest_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def _detect_edge_laser(image):
    """边缘增强检测 - 适用于黑线背景"""
    # 转换为灰度
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Sobel边缘检测
    sobelx = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
    sobel = np.sqrt(sobelx**2 + sobely**2)
    sobel = np.uint8(sobel)
    
    # 阈值处理
    _, edge_mask = cv2.threshold(sobel, 30, 255, cv2.THRESH_BINARY)
    
    # 亮度掩码
    _, bright_mask = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)
    
    # 组合边缘和亮度
    combined = cv2.bitwise_and(edge_mask, bright_mask)
    
    # 形态学闭运算
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(combined, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 选择最圆的轮廓
    best_contour = None
    best_circularity = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if 3 <= area <= 100:
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter ** 2)
                if circularity > best_circularity:
                    best_circularity = circularity
                    best_contour = contour
    
    if best_contour is not None and best_circularity > 0.3:
        M = cv2.moments(best_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

# 使用示例和测试函数
def test_single_image(image_path):
    """测试单张图片"""
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return
    
    laser_point = detect_laser_point(image)
    
    if laser_point:
        x, y = laser_point
        print(f"检测到激光点: ({x}, {y})")
        
        # 在图像上标记
        cv2.circle(image, (x, y), 10, (0, 255, 0), 2)
        cv2.putText(image, f"({x}, {y})", (x+15, y-15), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        cv2.imshow('Laser Detection Result', image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    else:
        print("未检测到激光点")

def test_video_stream(video_source=0):
    """测试视频流（摄像头或视频文件）"""
    cap = cv2.VideoCapture(video_source)
    
    if not cap.isOpened():
        print(f"无法打开视频源: {video_source}")
        return
    
    print("按 'q' 退出，按 's' 保存当前帧")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 检测激光点
        laser_point = detect_laser_point(frame)
        
        if laser_point:
            x, y = laser_point
            # 在图像上标记
            cv2.circle(frame, (x, y), 8, (0, 255, 0), 2)
            cv2.putText(frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            cv2.putText(frame, "Laser Detected", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "No Laser", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        cv2.imshow('Laser Detection', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('s') and laser_point:
            # 保存当前帧
            filename = f"laser_detection_{laser_point[0]}_{laser_point[1]}.jpg"
            cv2.imwrite(filename, frame)
            print(f"保存图像: {filename}")
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    # 使用示例
    print("激光点检测器 - 使用示例")
    print("1. 测试视频文件")
    print("2. 测试摄像头")
    print("3. 测试单张图片")
    
    choice = input("请选择测试模式 (1/2/3): ").strip()
    
    if choice == "1":
        test_video_stream("output.mp4")
    elif choice == "2":
        test_video_stream(0)  # 使用默认摄像头
    elif choice == "3":
        image_path = input("请输入图片路径: ").strip()
        test_single_image(image_path)
    else:
        print("无效选择，使用默认测试...")
        test_video_stream("output.mp4")
