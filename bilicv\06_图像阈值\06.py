# 250316

import cv2

img01 = cv2.imread("bilicv/img01.jpg")

ret, thresh01 = cv2.threshold(img01, 127, 255, cv2.THRESH_BINARY)
ret, thresh02 = cv2.threshold(img01, 127, 255, cv2.THRESH_BINARY_INV)
ret, thresh03 = cv2.threshold(img01, 127, 255, cv2.THRESH_TRUNC)
ret, thresh04 = cv2.threshold(img01, 127, 255, cv2.THRESH_TOZERO)
ret, thresh05 = cv2.threshold(img01, 127, 255, cv2.THRESH_TOZERO_INV)

cv2.imshow("a", img01)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("a", thresh01)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("a", thresh02)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("a", thresh03)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("a", thresh04)
cv2.waitKey(0)
cv2.destroyAllWindows()

cv2.imshow("a", thresh05)
cv2.waitKey(0)
cv2.destroyAllWindows()
