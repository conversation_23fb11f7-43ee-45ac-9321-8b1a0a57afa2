#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终比较分析工具
比较改进前后的检测效果，并与标注数据对比
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime

def compare_all_detectors():
    """比较所有检测器的性能"""
    # 加载标注数据
    annotation_file = "laser_annotations/quick_annotations_20250723_192904.json"
    with open(annotation_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 去除重复标注
    unique_annotations = []
    seen_frames = set()
    for ann in annotations:
        if ann['frame'] not in seen_frames:
            unique_annotations.append(ann)
            seen_frames.add(ann['frame'])
    
    print(f"加载标注数据: {len(unique_annotations)} 个唯一标注帧")
    
    # 导入检测器
    from improved_annotation_detector import ImprovedAnnotationDetector
    from final_enhanced_detector import detect_laser_point_final
    
    improved_detector = ImprovedAnnotationDetector()
    
    cap = cv2.VideoCapture('output.mp4')
    
    results = {
        'improved_detector': [],
        'final_enhanced_detector': [],
        'annotations': unique_annotations
    }
    
    print("开始比较分析...")
    
    for i, ann in enumerate(unique_annotations[:100]):  # 分析前100个标注帧
        frame_num = ann['frame']
        true_roi_x, true_roi_y = ann.get('roi_x'), ann.get('roi_y')
        
        if true_roi_x is None or true_roi_y is None:
            continue
        
        # 加载对应帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            frame = cv2.resize(frame, (640, 480))
            roi_frame = frame[65:410, 260:470, :]
            
            # 测试改进的检测器
            detected_improved = improved_detector.detect_laser_point(roi_frame)
            
            # 测试最终增强检测器
            detected_final = detect_laser_point_final(roi_frame)
            
            # 记录结果
            result_base = {
                'frame': frame_num,
                'true_position': (true_roi_x, true_roi_y)
            }
            
            # 改进检测器结果
            if detected_improved:
                error_x = abs(detected_improved[0] - true_roi_x)
                error_y = abs(detected_improved[1] - true_roi_y)
                distance_error = np.sqrt(error_x**2 + error_y**2)
                
                results['improved_detector'].append({
                    **result_base,
                    'detected_position': detected_improved,
                    'error_x': error_x,
                    'error_y': error_y,
                    'distance_error': distance_error,
                    'detection_success': True
                })
            else:
                results['improved_detector'].append({
                    **result_base,
                    'detected_position': None,
                    'detection_success': False
                })
            
            # 最终增强检测器结果
            if detected_final:
                error_x = abs(detected_final[0] - true_roi_x)
                error_y = abs(detected_final[1] - true_roi_y)
                distance_error = np.sqrt(error_x**2 + error_y**2)
                
                results['final_enhanced_detector'].append({
                    **result_base,
                    'detected_position': detected_final,
                    'error_x': error_x,
                    'error_y': error_y,
                    'distance_error': distance_error,
                    'detection_success': True
                })
            else:
                results['final_enhanced_detector'].append({
                    **result_base,
                    'detected_position': None,
                    'detection_success': False
                })
        
        if i % 20 == 0:
            print(f"已处理 {i+1}/100 帧")
    
    cap.release()
    
    # 分析结果
    analyze_all_results(results)
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_file = f"laser_annotations/final_comparison_{timestamp}.json"
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2)
    
    print(f"比较结果已保存到: {result_file}")

def analyze_all_results(results):
    """分析所有检测器的结果"""
    print("\n" + "=" * 80)
    print("最终检测器性能对比分析")
    print("=" * 80)
    
    detectors = ['improved_detector', 'final_enhanced_detector']
    detector_names = ['改进的标注检测器', '最终增强检测器']
    
    for detector, name in zip(detectors, detector_names):
        print(f"\n{name}:")
        print("-" * 40)
        
        detector_results = results[detector]
        successful = [r for r in detector_results if r.get('detection_success', False) and 'distance_error' in r]
        failed = [r for r in detector_results if not r.get('detection_success', False)]
        
        total = len(detector_results)
        success_count = len(successful)
        fail_count = len(failed)
        
        print(f"总测试帧数: {total}")
        print(f"成功检测: {success_count}")
        print(f"检测失败: {fail_count}")
        print(f"检测成功率: {success_count/total*100:.1f}%")
        
        if successful:
            errors = [r['distance_error'] for r in successful]
            
            print(f"误差统计:")
            print(f"  平均误差: {np.mean(errors):.2f} 像素")
            print(f"  中位数误差: {np.median(errors):.2f} 像素")
            print(f"  最大误差: {np.max(errors):.2f} 像素")
            print(f"  最小误差: {np.min(errors):.2f} 像素")
            print(f"  标准差: {np.std(errors):.2f} 像素")
            
            # 误差分布
            excellent = len([e for e in errors if e <= 5])
            good = len([e for e in errors if 5 < e <= 10])
            acceptable = len([e for e in errors if 10 < e <= 20])
            poor = len([e for e in errors if e > 20])
            
            print(f"误差分布:")
            print(f"  优秀 (≤5像素): {excellent} ({excellent/len(errors)*100:.1f}%)")
            print(f"  良好 (5-10像素): {good} ({good/len(errors)*100:.1f}%)")
            print(f"  可接受 (10-20像素): {acceptable} ({acceptable/len(errors)*100:.1f}%)")
            print(f"  较差 (>20像素): {poor} ({poor/len(errors)*100:.1f}%)")
    
    # 对比分析
    print(f"\n对比总结:")
    print("-" * 40)
    
    improved_success_rate = len([r for r in results['improved_detector'] if r.get('detection_success', False)]) / len(results['improved_detector']) * 100
    final_success_rate = len([r for r in results['final_enhanced_detector'] if r.get('detection_success', False)]) / len(results['final_enhanced_detector']) * 100
    
    print(f"改进检测器成功率: {improved_success_rate:.1f}%")
    print(f"最终增强检测器成功率: {final_success_rate:.1f}%")
    
    if improved_success_rate > final_success_rate:
        print("✓ 改进的标注检测器表现更好")
    else:
        print("✓ 最终增强检测器表现更好")
    
    # 计算平均误差
    improved_errors = [r['distance_error'] for r in results['improved_detector'] if r.get('detection_success', False) and 'distance_error' in r]
    final_errors = [r['distance_error'] for r in results['final_enhanced_detector'] if r.get('detection_success', False) and 'distance_error' in r]
    
    if improved_errors and final_errors:
        print(f"改进检测器平均误差: {np.mean(improved_errors):.2f} 像素")
        print(f"最终增强检测器平均误差: {np.mean(final_errors):.2f} 像素")
        
        if np.mean(improved_errors) < np.mean(final_errors):
            print("✓ 改进检测器精度更高")
        else:
            print("✓ 最终增强检测器精度更高")

def create_visual_comparison():
    """创建可视化比较"""
    # 加载标注数据
    annotation_file = "laser_annotations/quick_annotations_20250723_192904.json"
    with open(annotation_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 导入检测器
    from improved_annotation_detector import ImprovedAnnotationDetector
    
    improved_detector = ImprovedAnnotationDetector()
    
    cap = cv2.VideoCapture('output.mp4')
    
    # 选择几个代表性帧进行可视化
    test_frames = [10, 50, 100, 200, 300, 400, 500, 600]
    
    print("创建可视化比较...")
    
    for frame_num in test_frames:
        # 查找对应的标注
        annotation = None
        for ann in annotations:
            if ann['frame'] == frame_num:
                annotation = ann
                break
        
        if not annotation:
            continue
        
        # 加载帧
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
        ret, frame = cap.read()
        
        if ret:
            frame = cv2.resize(frame, (640, 480))
            display_frame = frame.copy()
            roi_frame = frame[65:410, 260:470, :]
            
            # 检测激光点
            detected = improved_detector.detect_laser_point(roi_frame)
            
            # 绘制ROI
            cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
            
            # 绘制真实位置
            if annotation.get('roi_x') is not None and annotation.get('roi_y') is not None:
                true_x = annotation['roi_x'] + 260
                true_y = annotation['roi_y'] + 65
                cv2.circle(display_frame, (true_x, true_y), 8, (0, 255, 0), 2)
                cv2.putText(display_frame, "TRUE", (true_x+15, true_y-15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 绘制检测位置
            if detected:
                det_x = detected[0] + 260
                det_y = detected[1] + 65
                cv2.circle(display_frame, (det_x, det_y), 8, (0, 0, 255), 2)
                cv2.putText(display_frame, "DETECTED", (det_x+15, det_y+15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
                
                # 计算误差
                if annotation.get('roi_x') is not None:
                    error = np.sqrt((detected[0] - annotation['roi_x'])**2 + 
                                  (detected[1] - annotation['roi_y'])**2)
                    cv2.putText(display_frame, f"Error: {error:.1f}px", (10, 100), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 显示帧信息
            cv2.putText(display_frame, f"Frame: {frame_num}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            
            # 保存比较图像
            filename = f"laser_annotations/comparison_frame_{frame_num:04d}.jpg"
            cv2.imwrite(filename, display_frame)
            print(f"保存比较图像: {filename}")
    
    cap.release()
    print("可视化比较完成")

def main():
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "visual":
        create_visual_comparison()
    else:
        compare_all_detectors()

if __name__ == "__main__":
    main()
