# 250715

import cv2
import cv2.xfeatures2d
import numpy as np

img = cv2.imread("bilicv\img01.jpg")
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# 得到特征点
sift = cv2.SIFT_create()
kp = sift.detect(gray, None)

img = cv2.drawKeypoints(gray, kp, img)

cv2.imshow('drawKeypoints', img)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 计算特征
kp, des = sift.compute(gray, kp)
print("kp.shape:", np.array(kp).shape)
print("des.shape:", des.shape)
print("des:", des[0])