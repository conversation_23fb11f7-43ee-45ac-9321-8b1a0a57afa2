# 250317

import cv2

img = cv2.imread("bilicv/img03.jpg")

gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

ret, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
contours, hierarchy = cv2.findContours(image=thresh, mode=cv2.RETR_TREE, method=cv2.CHAIN_APPROX_NONE)

draw_img = img.copy()
# res = cv2.drawContours(draw_img, contours, -1, (0, 255, 255), 1)

# cv2.imshow("a", gray)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

# cv2.imshow("a", res)
# cv2.waitKey(0)
# cv2.destroyAllWindows()

cnt = contours[22222]

# epsilon = 0.1*cv2.arcLength(cnt, True)
# approx = cv2.approxPolyDP(cnt, epsilon, True)
# show = cv2.drawContours(draw_img, [approx], -1, (0, 0, 255), 1)

# x, y, h, w = cv2.boundingRect(cnt)
# show = cv2.rectangle(draw_img, (x, y), (x+h, y+w), (0, 0, 255), 1)

(x, y), radius = cv2.minEnclosingCircle(cnt)
center = (int(x), int(y))
radius = int(radius)
show = cv2.circle(draw_img, center, radius, (0, 0, 255), 1)

cv2.imshow("a", show)
cv2.waitKey(0)
cv2.destroyAllWindows()

