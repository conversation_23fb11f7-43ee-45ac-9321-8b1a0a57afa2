#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jetson Nano优化的激光点检测器
专为低功耗设备设计，支持白纸和黑线背景
"""

import cv2
import numpy as np

def detect_laser_point_optimized(image):
    """
    优化的激光点检测函数 - 专为Jetson Nano设计
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    height, width = image.shape[:2]
    
    # 快速预处理：降采样以提高性能
    scale_factor = 1.0
    if width > 1280:  # 如果图像太大，先缩小
        scale_factor = 1280.0 / width
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        image = cv2.resize(image, (new_width, new_height))
    
    # 方法1: 快速亮度检测 (最高效)
    laser_point = _fast_brightness_detection(image)
    if laser_point is not None:
        # 如果进行了缩放，需要还原坐标
        if scale_factor != 1.0:
            x, y = laser_point
            x = int(x / scale_factor)
            y = int(y / scale_factor)
            return (x, y)
        return laser_point
    
    # 方法2: HSV颜色检测 (中等效率)
    laser_point = _fast_hsv_detection(image)
    if laser_point is not None:
        if scale_factor != 1.0:
            x, y = laser_point
            x = int(x / scale_factor)
            y = int(y / scale_factor)
            return (x, y)
        return laser_point
    
    # 方法3: 边缘增强检测 (适用于黑线背景)
    laser_point = _edge_enhanced_detection(image)
    if laser_point is not None:
        if scale_factor != 1.0:
            x, y = laser_point
            x = int(x / scale_factor)
            y = int(y / scale_factor)
            return (x, y)
        return laser_point
    
    return None

def _fast_brightness_detection(image):
    """快速亮度检测 - 适用于白纸背景"""
    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 自适应阈值：基于图像的平均亮度
    mean_brightness = np.mean(gray)
    threshold = max(200, mean_brightness + 50)
    
    # 二值化
    _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
    
    # 快速形态学操作
    kernel = np.ones((3, 3), np.uint8)
    binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 快速筛选：只考虑面积和位置
    best_point = None
    best_area = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        # 面积筛选
        if 5 <= area <= 300:
            # 计算质心
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # 选择面积最大的候选
                if area > best_area:
                    best_area = area
                    best_point = (cx, cy)
    
    return best_point

def _fast_hsv_detection(image):
    """快速HSV检测 - 适用于一般背景"""
    # 转换为HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # 优化的红色范围 (更宽松的阈值)
    lower_red1 = np.array([0, 100, 120])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([165, 100, 120])
    upper_red2 = np.array([180, 255, 255])
    
    # 创建掩码
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    mask = cv2.bitwise_or(mask1, mask2)
    
    # 简单的形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 选择最大的轮廓
    largest_contour = max(contours, key=cv2.contourArea)
    area = cv2.contourArea(largest_contour)
    
    if 3 <= area <= 500:
        M = cv2.moments(largest_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def _edge_enhanced_detection(image):
    """边缘增强检测 - 适用于黑线背景"""
    # 转换为灰度
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # 使用Sobel算子检测边缘 (比Laplacian更快)
    sobelx = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
    sobely = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
    sobel = np.sqrt(sobelx**2 + sobely**2)
    sobel = np.uint8(sobel)
    
    # 阈值处理
    _, edge_mask = cv2.threshold(sobel, 50, 255, cv2.THRESH_BINARY)
    
    # 亮度掩码
    _, bright_mask = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY)
    
    # 组合边缘和亮度
    combined = cv2.bitwise_and(edge_mask, bright_mask)
    
    # 形态学闭运算
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(combined, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 选择最圆的轮廓
    best_contour = None
    best_circularity = 0
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if 5 <= area <= 200:
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter ** 2)
                if circularity > best_circularity:
                    best_circularity = circularity
                    best_contour = contour
    
    if best_contour is not None and best_circularity > 0.3:
        M = cv2.moments(best_contour)
        if M["m00"] != 0:
            cx = int(M["m10"] / M["m00"])
            cy = int(M["m01"] / M["m00"])
            return (cx, cy)
    
    return None

def create_laser_detector():
    """
    创建一个激光检测器类，用于批量处理
    """
    class LaserDetector:
        def __init__(self):
            self.last_position = None
            self.detection_history = []
            self.max_history = 5
        
        def detect(self, image):
            """检测激光点位置"""
            position = detect_laser_point_optimized(image)
            
            # 简单的时间滤波
            if position is not None:
                self.detection_history.append(position)
                if len(self.detection_history) > self.max_history:
                    self.detection_history.pop(0)
                
                # 如果连续检测到，使用平均位置
                if len(self.detection_history) >= 3:
                    avg_x = sum(p[0] for p in self.detection_history) // len(self.detection_history)
                    avg_y = sum(p[1] for p in self.detection_history) // len(self.detection_history)
                    position = (avg_x, avg_y)
                
                self.last_position = position
            
            return position
        
        def get_last_position(self):
            """获取最后检测到的位置"""
            return self.last_position
    
    return LaserDetector()

# 简单的测试函数
def test_detection(image_path_or_camera=0):
    """
    测试激光点检测
    
    参数:
        image_path_or_camera: 图像路径或摄像头索引
    """
    if isinstance(image_path_or_camera, str):
        # 测试单张图片
        image = cv2.imread(image_path_or_camera)
        if image is None:
            print(f"无法读取图像: {image_path_or_camera}")
            return
        
        laser_point = detect_laser_point_optimized(image)
        
        if laser_point:
            print(f"检测到激光点: {laser_point}")
            # 在图像上标记
            cv2.circle(image, laser_point, 10, (0, 255, 0), 2)
            cv2.putText(image, f"({laser_point[0]}, {laser_point[1]})", 
                       (laser_point[0]+15, laser_point[1]-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            print("未检测到激光点")
        
        cv2.imshow('Result', image)
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    else:
        # 测试视频流
        cap = cv2.VideoCapture(image_path_or_camera)
        detector = create_laser_detector()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            laser_point = detector.detect(frame)
            
            if laser_point:
                cv2.circle(frame, laser_point, 8, (0, 255, 0), 2)
                cv2.putText(frame, f"({laser_point[0]}, {laser_point[1]})", 
                           (laser_point[0]+15, laser_point[1]-15), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            cv2.imshow('Laser Detection', frame)
            
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
        
        cap.release()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    # 测试视频文件
    test_detection("output.mp4")
