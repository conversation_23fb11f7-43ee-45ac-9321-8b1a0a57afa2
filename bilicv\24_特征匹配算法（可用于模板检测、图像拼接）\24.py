# 250716

import cv2
import numpy as np
import matplotlib.pyplot as plt

import time

def cv_show(img, name='xixi'):
        cv2.imshow(name, img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()

first = 0
if first == 1:
    img1 = cv2.imread('bilicv\img03.jpg')
    img2 = cv2.imread('bilicv\img04.jpg')

    time1 = time.perf_counter_ns()
    # Brute-Force蛮力匹配
    sift = cv2.SIFT_create()
    # SIFT特征提取需300+—10ms
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)
    bf = cv2.BFMatcher(crossCheck=True)
    # 注：crossCheck表示两个特征点要相互匹配，如A中第i个特征点与B中的第j个特征点最近的（相反也是）
    # NORM_L2表示归一化数组的（欧几里得距离），如果使用非BF特征计算方法需要考虑不同的匹配计算方式
    # 此方法匹配需300+-10ms

    # 1对1匹配
    matches = bf.match(des1, des2)
    matches = sorted(matches, key = lambda x : x.distance)

    img3 = cv2.drawMatches(img1, kp1, img2, kp2, matches[:500], None, flags = 2)
    print((time.perf_counter_ns() - time1)  / 1000000)

    cv_show(img3)



    # 1对多匹配（k对最佳匹配）
    time2 = time.perf_counter_ns()          # 此方法匹配需170+-10ms

    bf = cv2.BFMatcher()
    matches = bf.knnMatch(des1, des2, k=2)

    good = []
    for m, n in matches:
        if m.distance < 0.75 * n.distance:
            good.append([m])

    img3 = cv2.drawMatchesKnn(img1, kp1, img2, kp2, good, None, flags = 2)

    print((time.perf_counter_ns() - time2) / 1000000)
    cv_show(img3)
    # 该方法如需更快速，可尝试cv2.FlannBasedMatcher


# 随机抽样一致算法（RANSAC，Random sample consensus）
# 可用于稍有差异的特征提取、图像拼接
class Stitcher:
    # 拼接函数
    def stitch(self, images, ratio=0.75, reprojThresh=4.0, showMatches=False):
        (imageB, imageA) = images
        (kpsA, featuresA) = self.detectAndDescribe(imageA)
        (kpsB, featuresB) = self.detectAndDescribe(imageB)

        M = self.matchKeyPoints(kpsA, kpsB, featuresA, featuresB, ratio, reprojThresh)

        if M is None:
            return None
        
        (matches, H, status) = M
        result = cv2.warpPerspective(imageA, H , (imageA.shape[1] + imageB.shape[1], imageA.shape[0]))
        cv_show(result, "resault")
        result[0:imageB.shape[0], 0:imageB.shape[1]] = imageB
        cv_show(result, "resault")

        # if showMatches:
        #     vis = self.drawMatches(imageA, imageB, kpsA, kpsB, matches, status)

        #     return (result, vis)

    def detectAndDescribe(self, image):
        # gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        descriptor = cv2.SIFT_create()
        (kps, features) = descriptor.detectAndCompute(image, None)
        kps = np.float32([kp.pt for kp in kps])
        return (kps, features)
    
    def matchKeyPoints(self, kpsA, kpsB, featuresA, featuresB, ratio, reprojThresh):
        # 建立BF匹配器
        matcher = cv2.BFMatcher()

        # 使用knn检测来自A、B图的SIFT特征匹配对，k=2
        rawMatches = matcher.knnMatch(featuresA, featuresB, 2)

        matches = []
        for m in rawMatches:
            # 当最近距离跟次近距离的比值小于ratio值时，保留此匹配对
            if len(m) == 2 and m[0].distance < m[1].distance * ratio:
                # 存储两个点的索引值
                matches.append((m[0].trainIdx, m[0].queryIdx))

        # 当筛选后的匹配对大于4，计算视角变换矩阵
        if len(matches) > 4:
            ptsA = np.float32([kpsA[i] for (_, i) in matches])
            ptsB = np.float32([kpsB[i] for (i, _) in matches])

            # 计算视角变换矩阵
            (H, statues) = cv2.findHomography(ptsA, ptsB, cv2.RANSAC, reprojThresh)

            return (matches, H, statues)
        
    # def cv_show(img, name):
    #     cv2.imshow(name, img)
    #     cv2.waitKey(0)
    #     cv2.destroyAllWindows()

imageA = cv2.imread('bilicv/img06_1.jpg')
imageB = cv2.imread('bilicv/img06_2.jpg')

stitcher = Stitcher()
(result, vis) = stitcher.stitch([imageA, imageB], showMatches=True)

cv_show(vis)
cv_show(result)