# 250317

import cv2

img = cv2.imread("bilicv/img03.jpg", cv2.IMREAD_GRAYSCALE)

scharrx = cv2.<PERSON>harr(img, cv2.CV_64F, 1, 0)
scharrx = cv2.convertScaleAbs(scharrx)
scharry = cv2.Scharr(img, cv2.CV_64F, 0, 1)
scharry = cv2.convertScaleAbs(scharry)

scharr = cv2.addWeighted(scharrx, 0.5, scharry, 0.5, 0)

cv2.imshow("a", scharr)
cv2.waitKey(0)
cv2.destroyAllWindows()

laplacian = cv2.Laplacian(img, cv2.CV_64F)
laplacian = cv2.convertScaleAbs(laplacian)

cv2.imshow("a", laplacian)
cv2.waitKey(0)
cv2.destroyAllWindows()
