{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第8章 图像拼接"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8.1 简介"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当我们游览祖国的大好河山时，我们往往一边感慨山川的奇伟峻丽，一边遗憾自己没法用设备拍一张全景图，而只能拍一些离散的照片。实际上，当你学习完这一章的内容之后，或许你就可以自己实现全景图的生成。\n", "\n", "\n", "图像拼接 (Stitching)，是指将数张描绘同一场景的有重叠的图像（可能是不同时间、不同视角或者不同传感器获得的）拼接成一幅无缝的全景图或者高分辨率图像的技术。图像拼接在运动检测和跟踪、增强现实、分辨率增强、视频压缩和图像稳定等机器视觉领域有很大的应用。如图 8-1 所示，图像拼接的任务是将类似于图中的一系列离散的图像拼接成一张完整的图像，如图 8-2 所示。接下来，就让我们一起学习图像拼接的相关知识吧。\n", "\n", "<center>\n", "    <img style=\"border-radius: 0.3125em;\" \n", "    src=\"1.png\" width=800>\n", "    <br>\n", "    <div style=\"color:orange; \n", "    display: inline-block;\n", "    color: #999;\n", "    padding: 2px;\">图8-1 离散的风景</div>\n", "</center>\n", "\n", "\n", "<center>\n", "    <img style=\"border-radius: 0.3125em;\" \n", "    src=\"2.png\" width=500>\n", "    <br>\n", "    <div style=\"color:orange; \n", "    display: inline-block;\n", "    color: #999;\n", "    padding: 2px;\">图8-2 图像拼接举例</div>\n", "</center>\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8.2 图像变换"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在正式介绍图像拼接之前，我们先介绍图像变换的相关工作。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2.1 平移"]}, {"cell_type": "markdown", "metadata": {}, "source": ["图像$\\mathbf{I}$上一点$\\mathbf{p}=(i,j)$，平移后的点为$\\mathbf{p}^{\\prime} = (i^{\\prime}, j^{\\prime})$，我们有\n", "\n", "$$\\mathbf{p}^{\\prime} = \\mathbf{p} + \\mathbf{t}$$\n", "\n", "其中，$\\mathbf{t} = (t_x, t_y)$为平移方向。平移前后图像的形状不发生变化。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2.2 旋转"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们将图像$\\mathbf{I}$上一点$\\mathbf{p}=(i,j)$逆时针旋转$\\theta$角度，则对应的新的点坐标为：\n", "\n", "$$\\mathbf{p}^{\\prime} = \\mathbf{R}\\mathbf{p}$$\n", "\n", "其中，\n", "\n", "$$\\mathbf{R} = \\begin{bmatrix}\n", "\\cos\\theta &-\\sin\\theta \\\\\n", "\\sin\\theta & \\cos\\theta\n", "\\end{bmatrix}$$\n", "\n", "是旋转矩阵。旋转前后图像的长度不发生变化。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2.3 相似"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们将图像$\\mathbf{I}$上一点$\\mathbf{p}=(i,j)$进行相似变换，则对应的新的点坐标为：\n", "\n", "$$\\mathbf{p}^{\\prime} = s\\mathbf{R}\\mathbf{p} + \\mathbf{t}$$\n", "\n", "其中，$s$为常数，$\\mathbf{t}$为平移量。图像相似变换前后角度不变"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2.4 仿射变换"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们将图像$\\mathbf{I}$上一点$\\mathbf{p}=(i,j)$进行仿射变换，则对应的新的点坐标为：\n", "\n", "$$\\mathbf{p}^{\\prime} = \\begin{bmatrix}\n", "a_{11} & a_{12} & a_{13} \\\\\n", "a_{21} & a_{22} & a_{23}\n", "\\end{bmatrix}[\\mathbf{p}, 1]^\\mathbf{T}$$\n", "\n", "其中，$s$为常数，$\\mathbf{t}$为平移量。仿射变换前后图像中的平行关系不变"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2.5 投影"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们将图像$\\mathbf{I}$上一点$\\mathbf{p}=(i,j)$投影到三维空间中，则：\n", "\n", "$$\\mathbf{p}^{\\prime} = \\mathbf{H}[\\mathbf{p}, 1]^\\mathbf{T}$$\n", "\n", "这里，$\\mathbf{H}$是一个$3\\times3$的投影矩阵。投影前后图像中的直线仍然为直线。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8.3 图像拼接"]}, {"cell_type": "markdown", "metadata": {}, "source": ["图像拼接是将同一场景的多个重叠图像拼接成较大的图像的一种方法，在医学成像、计算机视觉、卫星数据、军事目标自动识别等领域具有重要意义。图像拼接的输出是两个输入图像的并集。图像拼接通常用到以下四个步骤：\n", "\n", "1. 图像特征提取\n", "2. 图像配准\n", "3. RANSAC算法计算单应矩阵\n", "4. 图像变形与融合"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.3.1 图像特征提取"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们如果要匹配的两个输入图像中的元素，首先需要提取图像的特征点。还记得我们在上一章学过的SIFT算法吗？它是尺度不变的特征点检测算法，可用于识别图像中的相似目标。SIFT算法既具有旋转不变性，又具有尺度不变性，因此非常适合于高分辨率图像中的目标检测。我们可以利用SIFT算法进行图像特征的提取。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.3.2 图像配准"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在两个输入图像$\\mathbf{I}_1,\\mathbf{I}_2$的特征点被检测出来之后，我们需要以某种方式将它们关联起来，可以通过计算归一化互相关或互信息的方法来确定其对应关系。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8.3.2.1 归一化互相关（Normalized Cross Correlation，NCC）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们在模板匹配中学习过互相关与标准化互相关的相关概念，这里我们进一步介绍另一种形式的互相关————归一化互相关。\n", "\n", "互相关的工作原理是将第一幅图像中每个点周围的像素窗口与第二幅图像中每个点周围的像素窗口关联起来，将双向相关性最大的点作为对应的“对”。我们定义归一化互相关如下：\n", "\n", "$$NCC(i,j) = \\frac{\\sum_{m}\\sum_n [\\mathbf{I}_1[m,n] - \\overline{\\mathbf{I}}_1] \\cdot [\\mathbf{I}_2[m+i, n+j] - \\overline{\\mathbf{I}}_2]}{\\sqrt{\\sum_{m}\\sum_n[\\mathbf{I}_1[m,n] - \\overline{\\mathbf{I}}_1]^2}\\sqrt{\\sum_{m}\\sum_n[\\mathbf{I}_2[m+i, n+j] - \\overline{\\mathbf{I}}_2]^2}}$$\n", "\n", "其中，$\\overline{\\mathbf{I}}_1,\\overline{\\mathbf{I}}_2$是窗口的平均值图像，即：\n", "\n", "$$\\overline{\\mathbf{I}}_1 = \\frac{1}{MN}\\sum_{i=1}^M\\sum_{j=1}^N \\mathbf{I}_1(i,j)$$\n", "\n", "$$\\overline{\\mathbf{I}}_2 = \\frac{1}{MN}\\sum_{i=1}^M\\sum_{j=1}^N \\mathbf{I}_2(i,j)$$\n", "\n", "NCC系数的范围为$[-1,1]$。我们令$u=(i,j)$，NCC峰值相对应的位移参数$u$表示为两个图像之间的几何变换。此方法的优点是计算简单，但是速度特别慢。此外，此类算法要求源图像之间必须有显著的重叠。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8.3.2.2 互信息（Mutual Information，MI）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["互信息测量基于两个图像之间共享信息数量的相似性，互信息越大，说明彼此之间越相似。两个图像$\\mathbf{I}_1,\\mathbf{I}_2$之间的互信息用熵表示为：\n", "\n", "$$MI(\\mathbf{I}_1,\\mathbf{I}_2) = E(\\mathbf{I}_1) + E(\\mathbf{I}_2) - E(\\mathbf{I}_1\\mathbf{I}_2)$$\n", "\n", "其中，$E(\\mathbf{I}_1)$和$E(\\mathbf{I}_2)$分别是图像$\\mathbf{I}_1$和$\\mathbf{I}_2$的熵，$E(\\mathbf{I}_1\\mathbf{I}_2)$是两个图像的联合熵。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8.3.3 单应矩阵"]}, {"cell_type": "markdown", "metadata": {}, "source": ["单应矩阵估计是图像拼接的第三步。在单应矩阵估计中，不属于重叠区域的不需要的特征点被删除。我们采用RANSAC算法进行单应。\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8.3.3.1 RANSAC 算法"]}, {"cell_type": "markdown", "metadata": {}, "source": ["随机样本一致算法RANSAC(random sample consensus)从可能含有异常值的观测数据集中拟合数学模型，是一种鲁棒参数估计的迭代方法。该算法是不确定性的，因为它只在一定的概率下产生一个合理的结果，随着执行更多的迭代，这个概率会增加。RANSAC算法用于在存在大量可用数据外行的情况下以鲁棒的方式拟合模型，在计算机视觉中有许多应用。\n", "\n", "算法的流程可以简要地描述如下：\n", "\n", "1. 从数据样本$S$中随机选取$K$个点，得到假设子集$S_K$，然后使用$S_K$优化模型参数$M(\\theta)$，得到初始模型；\n", "2. 对所有数据样本$S$应用模型$M(\\theta)$，根据每个数据样本的误差$||M(\\theta, x_i)-y_i||$来所有数据样本划分为内群点（inliner points）和离群点（outliner points），其中误差大于超参数$\\epsilon$的称为离群点$S_{outliner}$，反之则为内群点$S_{inliner}$；\n", "3. 如果内群点的占比$R = \\frac{S_{inliner}}{S}$大于超参数阈值$\\tau$，则使用所有的内群点$S_{inliner}$来重新优化模型参数，并将得到的模型作为最终模型，终止迭代；\n", "4. 否则使用内群点$S_{inliner}$优化模型，并重复执行步骤 2，直到条件3满足，或者达到迭代次数上限停止。\n", "\n", "图 8-3 是使用RANSAC拟合数据的一个流程。\n", "\n", "\n", "<center>\n", "    <img style=\"border-radius: 0.3125em;\" \n", "    src=\"3.png\" width=500>\n", "    <br>\n", "    <div style=\"color:orange; \n", "    display: inline-block;\n", "    color: #999;\n", "    padding: 2px;\">图8-3 RANSAC示意</div>\n", "</center>\n", "\n", "可以看到 RANSAC 算法的性能由超参数$N,K,\\epsilon,\\tau$来决定，通常来讲，迭代次数必须设置的足够大，才能使得至少有一次数据采样$S_K$不包含任何离群点的概率$p$（一般来说$p=0.99$）满足我们的使用要求，以此来避免离群点的影响。我们可以通过简单地推导来得到迭代上限的近似估计:\n", "\n", "假设每次随机选择一个点，该点为内群点的概率为$p_{i}$，则其为离群点的概率为$p_o = 1-p_i$ ，则在$N$次迭代，每次迭代选取$K$个点的情况下，有\n", "\n", "$$1-p = (1-p_i^K)^N$$\n", "\n", "从而估计出\n", "\n", "$$N = \\frac{\\log(1-p)}{\\log(1-(1-p_i)^K)}$$\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8.3.3.2 计算单应矩阵"]}, {"cell_type": "markdown", "metadata": {}, "source": ["有了两组相关点，接下来就需要建立两组点的转换关系，也就是图像变换关系。单应矩阵是两个空间之间的映射，常用于表示同一场景的两个图像之间的对应关系。我们可以利用单应矩阵匹配大部分相关的特征点，并实现图像投影，使一张图通过投影和另一张图重合。\n", "\n", "假设两个图像的匹配点为$\\mathbf{X} = [x,y]^\\mathbf{T}, \\mathbf{X}^{\\prime} = [x^{\\prime},y^{\\prime}]^\\mathbf{T}$\n", "\n", "我们有\n", "\n", "$$\\mathbf{X}^{\\prime} = \\mathbf{H}[\\mathbf{X},1]$$\n", "\n", "由于$\\mathbf{X}$和$\\mathbf{X}^{\\prime}$两向量共线，我们有\n", "\n", "$$[\\mathbf{X}^{\\prime},1]^\\mathsf{T}\\mathbf{H}[\\mathbf{{X}},1] = 0$$\n", "\n", "令$\\mathbf{H} = \\begin{bmatrix}\n", "h_{11} & h_{12} & h_{13} \\\\\n", "h_{21} & h_{22} & h_{23} \\\\\n", "h_{31} & h_{32} & 1\n", "\\end{bmatrix}$为一个含有8个参数的变换矩阵，由此我们知道四点可以确定$\\mathbf{H}$，即：\n", "\n", "\\begin{equation}\n", "\\left[\\begin{array}{c}\n", "x_{1}, y_{1}, 1,0,0,0,-x_{1}^{\\prime} x_{1},-x_{1}^{\\prime} y_{1} \\\\\n", "0,0,0, x_{1}, y_{1}, 1,-y_{1}^{\\prime} x_{1},-y_{1}^{\\prime} y_{1} \\\\\n", "x_{2}, y_{2}, 1,0,0,0,-x_{2}^{\\prime} x_{2},-x_{2}^{\\prime} y_{2} \\\\\n", "0,0,0, x_{2}, y_{2}, 1,-y_{2}^{\\prime} x_{2},-y_{2}^{\\prime} y_{2} \\\\\n", "x_{3}, y_{3}, 1,0,0,0,-x_{3}^{\\prime} x_{3},-x_{3}^{\\prime} y_{3} \\\\\n", "0,0,0, x_{3}, y_{3}, 1,-y_{3}^{\\prime} x_{3},-y_{3}^{\\prime} y_{3} \\\\\n", "x_{4}, y_{4}, 1,0,0,0,-x_{4}^{\\prime} x_{4},-x_{4}^{\\prime} y_{4} \\\\\n", "0,0,0, x_{4}, y_{4}, 1,-y_{4}^{\\prime} x_{4},-y_{4}^{\\prime} y_{4}\n", "\\end{array}\\right]\\left[\\begin{array}{l}\n", "h_{12} \\\\\n", "h_{21} \\\\\n", "h_{22} \\\\\n", "h_{23} \\\\\n", "h_{31} \\\\\n", "h_{32}\n", "\\end{array}\\right]=\\left[\\begin{array}{c}\n", "x_{1}^{\\prime} \\\\\n", "y_{1}^{\\prime} \\\\\n", "x_{2}^{\\prime} \\\\\n", "y_{2}^{\\prime} \\\\\n", "x_{3}^{\\prime} \\\\\n", "y_{3}^{\\prime} \\\\\n", "x_{4}^{\\prime} \\\\\n", "y_{4}^{\\prime}\n", "\\end{array}\\right]\n", "\\end{equation}\n", "\n", "那么就可以每次从所有的匹配点中选出4对，计算单应性矩阵H，然后选出内点个数最多的作为最终的结果。其中，特征点的误差定义为：\n", "\n", "$$Dist(\\mathbf{HX}、\\mathbf{X}^{\\prime}) = \\left\\|\\left[\\begin{array}{c}\n", "x^{\\prime} \\\\\n", "y^{\\prime} \\\\\n", "1\n", "\\end{array}\\right]-\\mathbf{H}\\left[\\begin{array}{c}\n", "x \\\\\n", "y \\\\\n", "1\n", "\\end{array}\\right]\\right\\|$$\n", "\n", "我们利用用RANSAC方法估算$\\mathbf{H}$的过程如下：\n", "\n", "1. 首先检测两张图像的特征点；\n", "2. 在特征点之间应用归一化互相关，收集相关性足够高的对；\n", "3. 使用匹配的对计算矩阵$\\mathbf{H}$；\n", "4. 选择与单应性一致的配对。如果有$Dist(\\mathbf{HX}、\\mathbf{X}^{\\prime}) <\\epsilon$，则点对$(\\mathbf{X},\\mathbf{X}^{\\prime})$被认为与单应性H一致\n", "5. 重复34步，直到足够多的点对满足$\\mathbf{H}$；\n", "6. 使用所有满足条件的点对，通过公式重新计算$\\mathbf{H}$\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.3.4 图像变形与融合"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最后一步是将所有输入图像变形并融合到一个符合的输出图像中。我们可以简单地将所有输入的图像变形到一个平面上，这个平面名为复合全景平面。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8.3.4.1 图像变形步骤"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. 计算每个输入图像的变形图像坐标范围，得到输出图像大小。我们可以很容易地通过映射每个源图像的四个角确定输出图像的大小。最后，需要计算指定参考图像原点相对于输出全景图的偏移量的偏移量；\n", "2. 使用上述的变形，将每个输入图像的像素映射到参考图像定义的平面。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8.3.4.2 图形融合"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当图像变形完成并展示在一张图像上之后，我们需要在重叠区域融合像素颜色以避免接缝。最简单的可用形式是使用羽化，它使用加权平均颜色值融合重叠的像素。我们通常使用$\\alpha$因子（通常称为$\\alpha$通道），它在中心像素处的值为1，在与边界像素线性递减后变为0。当输出拼接图像中至少有两幅重叠图像时，我们将使用如下的$\\alpha$值来计算其中一个像素处的颜色.\n", "\n", "假设两个图像$\\mathbf{I}_1,\\mathbf{I}_2$在输出图像中重叠，每个像素点$(x,y)$在图像中表示为$\\mathbf{I}_i(x,y) = (\\alpha_iR, \\alpha_iG, \\alpha_iB, \\alpha_i)$，其中$(R,G,B)$是像素的颜色值。我们将在缝合后的输出图像中计算$(x,y)$的像素值为：\n", "\n", "$$\\frac{[(\\alpha_1R, \\alpha_1G, \\alpha_1B, \\alpha_1)+[(\\alpha_2R, \\alpha_2G, \\alpha_2B, \\alpha_2)]}{\\alpha_1+\\alpha_2}$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8.4 编程实现"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在这一节中，我们将编程实现图像拼接。我们先导入上一章中编写的SIFT相关函数，并在此基础上利用RANSAC算法实现图像拼接。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from utils import *\n", "def draw_matches(image1, image2, corners1, corners2, matches,\n", "    outlier_labels=None):\n", "    \"\"\"Draw matched corners between images.\n", "    \n", "    Args:\n", "    - matches (list of 2-tuples)\n", "    - image1 (3D uint8 array): A color image having shape (H1, W1, 3).\n", "    - image2 (3D uint8 array): A color image having shape (H2, W2, 3).\n", "    - corners1 (list of 2-tuples)\n", "    - corners2 (list of 2-tuples)\n", "    - outlier_labels (list of bool)\n", "    \n", "    Returns:\n", "    - match_image (3D uint8 array): A color image having shape\n", "        (max(H1, H2), W1 + W2, 3).\n", "    \"\"\"\n", "    h1, w1 = image1.shape\n", "    h2, w2 = image2.shape\n", "    hres = 0\n", "    if h1 > h2:\n", "        hres = int((h1 - h2) / 2)\n", "\n", "    match_image = np.zeros((h1, w1 + w2, 3), np.uint8)\n", "\n", "    for i in range(3):\n", "        match_image[:h1, :w1, i] = image1\n", "        match_image[hres:hres + h2, w1:w1 + w2, i] = image2\n", "\n", "    for i in range(len(matches)):\n", "        m = matches[i]\n", "        pt1 = (int(corners1[m[0]][0]), int(corners1[m[0]][1]))\n", "        pt2 = (int(corners2[m[1]][0] + w1), int(corners2[m[1]][1] + hres))\n", "        cv2.circle(match_image, pt1, 1, (0,255,0), 2)\n", "        cv2.circle(match_image, (pt2[0], pt2[1]), 1, (0,255,0), 2)\n", "        if outlier_labels[i] == 1:\n", "            cv2.line(match_image, pt1, pt2, (255, 0, 0))\n", "        else:\n", "            cv2.line(match_image, pt1, pt2, (0, 0, 255))\n", "        \n", "    return match_image\n", "\n", "\n", "# 利用RANSAC算法剔除无效点\n", "def compute_affine_xform(corners1, corners2, matches):\n", "    \"\"\"Compute affine transformation given matched feature locations.\n", "    \n", "    Args:\n", "    - corners1 (list of 2-tuples)\n", "    - corners1 (list of 2-tuples)\n", "    - matches (list of 2-tuples)\n", "    \n", "    Returns:\n", "    - xform (2D float64 array): A 3x3 matrix representing the affine\n", "        transformation that maps coordinates in image1 to the corresponding\n", "        coordinates in image2.\n", "    - outlier_labels (list of bool): A list of Boolean values indicating whether\n", "        the corresponding match in `matches` is an outlier or not. For example,\n", "        if `matches[42]` is determined as an outlier match after RANSAC, then\n", "        `outlier_labels[42]` should have value `True`.\n", "    \"\"\"\n", "    iteration = 50\n", "    M_list = []\n", "    inlier_num_list = []\n", "    for _ in range(iteration):\n", "        sample_index = random.sample(range(len(matches)), 4)\n", "        x1_s, y1_s = corners1[matches[sample_index[0]][0]]\n", "        x1_t, y1_t = corners2[matches[sample_index[0]][1]]\n", "        x2_s, y2_s = corners1[matches[sample_index[1]][0]]\n", "        x2_t, y2_t = corners2[matches[sample_index[1]][1]]\n", "        x3_s, y3_s = corners1[matches[sample_index[2]][0]]\n", "        x3_t, y3_t = corners2[matches[sample_index[2]][1]]\n", "        x4_s, y4_s = corners1[matches[sample_index[3]][0]]\n", "        x4_t, y4_t = corners2[matches[sample_index[3]][1]]\n", "\n", "        A = np.array([[x1_s, y1_s, 1, 0, 0, 0, -x1_t*x1_s, -x1_t*y1_s, -x1_t],\n", "                      [0, 0, 0, x1_s, y1_s, 1, -y1_t*x1_s, -y1_t*y1_s, -y1_t],\n", "                      [x2_s, y2_s, 1, 0, 0, 0, -x2_t*x2_s, -x2_t*y2_s, -x2_t],\n", "                      [0, 0, 0, x2_s, y2_s, 1, -y2_t*x2_s, -y2_t*y2_s, -y2_t],\n", "                      [x3_s, y3_s, 1, 0, 0, 0, -x3_t*x3_s, -x3_t*y3_s, -x3_t],\n", "                      [0, 0, 0, x3_s, y3_s, 1, -y3_t*x3_s, -y3_t*y3_s, -y3_t],\n", "                      [x4_s, y4_s, 1, 0, 0, 0, -x4_t*x4_s, -x4_t*y4_s, -x4_t],\n", "                      [0, 0, 0, x4_s, y4_s, 1, -y4_t*x4_s, -y4_t*y4_s, -y4_t]\n", "                      ])\n", "\n", "        _,_, v = np.linalg.svd(A)\n", "        M = np.reshape(v[-1], (3, 3))\n", "        \n", "        #M = t.reshape((3,3))\n", "        inlier_num = 0\n", "        for (index1, index2) in matches:\n", "            coord1 = [corners1[index1][0], corners1[index1][1], 1]\n", "            coord2 = [corners2[index2][0], corners2[index2][1], 1]\n", "            mapcoor = np.dot(M, coord1)\n", "            mapcoor = mapcoor/mapcoor[-1]\n", "            if np.linalg.norm(coord2 - mapcoor) < 5:\n", "                inlier_num += 1\n", "        M_list.append(M)\n", "        inlier_num_list.append(inlier_num)\n", "        \n", "    best_index = np.argmax(inlier_num_list)\n", "    xform = M_list[best_index].astype(np.float64)\n", "    outlier_labels = []\n", "    for (index1, index2) in matches:\n", "        coord1 = [corners1[index1][0], corners1[index1][1], 1]\n", "        coord2 = [corners2[index2][0], corners2[index2][1], 1]\n", "        mapcoor = np.dot(xform, coord1)\n", "        mapcoor = mapcoor/mapcoor[-1]\n", "        if np.linalg.norm(coord2 - mapcoor) < 12:\n", "            outlier_labels.append(1)\n", "        else:\n", "            outlier_labels.append(0)\n", "    \n", "    return xform, outlier_labels  \n", "\n", "\n", "def stitch_images(image1, image2, xform):\n", "    \"\"\"<PERSON><PERSON> two matched images given the transformation between them.\n", "    \n", "    Args:\n", "    - image1 (3D uint8 array): A color image.\n", "    - image2 (3D uint8 array): A color image.\n", "    - xform (2D float64 array): A 3x3 matrix representing the transformation\n", "        between image1 and image2. This transformation should map coordinates\n", "        in image1 to the corresponding coordinates in image2.\n", "    \n", "    Returns:\n", "    - image_stitched (3D uint8 array)\n", "    \"\"\"\n", "    new_image = cv2.warpPerspective(image1, xform, (image2.shape[1],image2.shape[0]))\n", "    image = new_image.copy()\n", "    \n", "    h, w, _ = image1.shape\n", "    pts = np.float32([[0, 0],\n", "                      [0, h - 1],\n", "                      [w - 1, h - 1],\n", "                      [w - 1, 0]]).reshape(-1, 1, 2)\n", "    dst = cv2.perspectiveTransform(pts, xform)\n", "\n", "    image = cv2.polylines(image2, [np.int32(dst)], True, 255, 1, cv2.LINE_AA)\n", "    image = np.where(new_image>0, 0.5*new_image+0.5*image, image)\n", "    return image.astype(np.uint8)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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***************************************+lBHGjnLTsvOchx/5tvCn5EuOKrX/0qnnrqKTz88MN47rnnJFvjqVJeXo4zZ84gHo/jxIkT0Ol0OHfuHObm5tDU1ISOjg5UV1fDZrPJqHZ2iLnQKCdWDX3YweTpz7KLD4BcUJUXOTU1hWg0Klkv32dhYQFf/vKXYTAY4PF4cObMGdxxxx2S9bJbT1+KtrY2wY6ZMXKK7vT0NE6dOiWb2uPxyNwsk8kkQhWr1Sqj7LkwiBWySbVmzRrZqGycsURWM83Z2VmsWLFCyjeHwwGfzyfwEnmrc3NzsNvtUm4CELYGDywAAocEg0EUFxfjYx/7GDZs2FCAH7PEbW5e9mfrZHx8QjboU089Bb/fjyeffBJ/+7d/i2QyiWAwiLq6OlRVVcHv96Oqqgo9PT1oa2tDWVkZ+vr60NTUBLPZjEAggJ6eHmzZsgWhUEjoRrQZ7e7uRltbG2ZnZzExMSEZOScc2Gw2hMNhwWKZZLBL7nQ6EYvF4PV6RTFoMpng9/ux609/uvhmuMR16OBBaXaykmLWT0tPcuhLS0sFE2aiwGdK5pHqLc2DmVhucXHewUvlxnNP0zaT1C6W11yzZPMQi5+ZmUFNTQ1mZ2fxta99DX19ffJ63NMVFRX47ne/i5KSEjz66KN44403sH79erz22mtSramNOVbF8Xgchw8fFmghFouhubkZra2t4mFdU1NTMFRAhTaYeC3N/hlQ+V35e1ZxlZWVkgyxcuaeS6fTCIfDAIAdO3ZcXqb7xhtvoK+vD1dffTWOHTuG2267DZWVlejq6sIPfvADPPvss2JNyPEaHLJYWVkJh9OBNW1OyTZVvuVHPvIR3HYee7VYLAgEAjCbzSI8uO+++7B3717BePjQi4uLsXbtWoyNjSEWi6Gvr68ABjh79izWrl0rpSZHehD3cldXX9bCP9fTI1nHyMgIpqamsHz5cthsNhw5cgR/+MMfkM3mpwjcfPPNMBgMkimpWQK7wFx0xOCYNfPn+P+qUxJxOJWVQTCfGZS6Odg5Z0Ybi8UkI/J4PMLAyGazAgnxEGDZNDU1herqarmPlZWVYkbC8pebORqNSgbAzxiJRMQPg4fX7373O/z7v/87Nm3ahI985COSBeYnBeQPjupqN8bHJ86r6/INpE9+8pPYunUrbrzxRgCQ93E4HNi9+9WC53XzzbdIx7qpqUn45ACwYsUKwTvZpaeQZu3atcjl8lNIamtrJQNyu92YmJgQCt/w8DAWFhZQX18vBwx7CB6PB0VFRejr60MikcCdd94JAPjADTcILjw3N4eWlpYCDim9hhsbG9HQ0ACbzYaKigrMTk9LxplOp2X0Dkt7ZvVqAJifn5eZeszuksmkTK2NRCJSiTJLa1uz5n+0J/7w+98L7MikKJ1O46477wIAvPTySwgEAlI9qKZJDL5Un5WVleG73/0uDh06hLvuugsnTpxAY2OjrEMA4pvLSd92u11kyhS2lJaWorOzUwzNCR8BF9SiqgiJF/Fbo9EIrVYLt9st64NVLPsTlZWVsk+Z5RJqomPcpa5LBl1mOHv37sX09DSOHTuGiYkJmbsVDofh9/sFtyHlhpCACogDkCyQNCuWw+yOJpNJ/PM//zO6u7txzz334IEHHsCHP/xhoWSpASaXyyEej+Pll1+Gw+FAQ0MDGhsbMTg4CIvFIlkuIQT6LMS7uiSwc6HSKJ3BnQuaGJlWq4X1vKLq7NmzGBgYwBtvvIGqqiqsW7cOJ06cQG1trcALBw4cgN1uh06nE+klHwhLFY4nofBDpeQx2P7oxz9+f6v/Xa7rr7sOdrtdvg8znWw2K5xXwh1kHHAcSiwWQ39/v8zJUhkXZrNZPjtntU1PTwsthwGcA0bHx8eh1WrR2NhYMLnCbDajt7cXf//3f49MJoPPfvazaG9vF5J6X18/NJoLo4aeeuop3H///ejt7YXNZpOeAn01HnjgQTlQ8o1Ul3TXmY0xE2GgUWXSAHDvvR8RA3Rmt7W1tQIlcMQ9ubcOh0MOUEItDocDtbW10uxpbGzE22+/jVOnTqG2tlbw2nA4jEgkgtbWVkycn4FG/HFkZARdXV0oKyvDXXfdJa5gXDs8bIl7J5PJApGFRqMR3JewHoNCKpUSQ3cGRFZGx44eLZjoQOobAIEcVD+K8vMBnv+eENGuP+3CzTfdjEwmI81d3h/uR0IkFRUViEajSKVSaGxsxH333Yff/va3uO+++/DSSy/JWiWcQt49Obh79+4VGJQxZ/Xq1UilUvKs2XdhT4qGPGps0mguTL5QG47EcdWmaSgUEuyWTVBVSet6j2nKl4QXrrnmmhwbWclkEp/+9Kexe/duHD58GG1tbeKk39vbix07dmDdunWCr5aWluL222+XQYO5XE6CGHFGlVeoMgSIG1MnzY3DVJ8YFwA5iSjzZMfYbrdLg4DNK4o5qDOnQICWkmo2vhT7+ad/+ifceOONSKVSOHjwoGCzxL0qKiqE71dUlB/jzkxraGhIFDj0ds1ms9IUI6WMgZE4ZygUgs/nw+LiItavXy+ff2FhQawzA4EALBaLYE8snaanp2G325FMJmEymYQXzFN4xYoVCIfDCAaD0tCorq6WhlE4HIbFYsHU1BTa29tljH1DQwOmpqYwNjaG4uJiOXQXFxex/6233vs0eJfrofMih0QigZqaGjzyyCNyQJM6uHfvXuzduxdPPfWUHIgf//jHpeRbXFyU5qTP50NDQ4MYXbtcLkQiEYRCIaxevVpoQvX19QUBk1g9qYJGoxF+v18aht3d3XjttT1/9vlbWpajrKwMDocDZrMZyWQSDocD3d3dwgNWaUr79r1x0XvxyU/+H4jH43j+13mzlY8+8lE88sgjknUxu+JzBi6Y6hMqIKbLAKya96giCsJiND/naxBHJZ2MmR4zVWb2pExyj/C5fOELX8B3vvOdAunw008/jUOHDhXQAgkXfv3rX5cA9sUvfhGf+tSnAACvv/46HnroIfnuzKp50D/zzDOYmZlBKBSC2+3GzMwMrr76akmgWltb4fV65XMzHtDkiBjsUkEJjYb4Z2Qu8P9V2hvhCVYQ//Zv/4aHHnoIq1atujxF2o4dO3J2ux3ZbBZVVVXYuHEjjh8/juHhYUxNTUnXPZVKobW1FdPT03A4HHjssccwPT2NRx55RDJO3mx+Yb1eLzPlyfHjw+RpFovFRPKpYjJqqc3uLm8WFxsxFxWr5Gtz4agUMJ7ALDX5HirmU1ZWhmg0CovFUpBtqBw9dUKBusj5s4QoCDkQAuD3UAntNB0hwZwVAd30mVFaLBbpJvN0B/LY69jYmNyfTCYj1KJkMgm/3w+z2SyMkpqaGrhcLszMzGBiYkLUVRs3bkQikZBGaUtLC06ePAmPx4OZmRl/ex4MAAAgAElEQVQJ/PF4XMQL9OQghGK1WgsOQEIpzFb5mVWLP7/fjzvuuAO/P1/Gfuc73ykQQNASlCVfWVmZ8DhjsViB2xYzSMq5KZsmA6SsrAy/+tV/XHQvPPLIo9Bqtfjxs89e9Gfe63rg/vvz48rr6vD973/vz/7+a1/7O8HfY7EYxsbGRE0ZjUbx4IMPipCDUlZmWWVlZbLOeX+YODBAkrPLzFOj0QilitOMOaWBpThfl3sXQAGcQP8NAELXy2azWLVqleyD4uJifOtb38LRo0cLmnhFRUUwm8148sknJfutrKzEX//1X+MHP/gBdu3ahW3btsnPl5aWwmazIRgM4ty5cwiFQjh8+HDBVHAKdrxeLzo6OmQ9salIChyrP65HXmyUM8NVufkACmhsPFTS6TT+8R//EaFQSAgEL7744uVhuslkUhQXq1atQjQaxSuvvCIAdSAQENlpVVUVxsfH4fF48Lvf/Q7z8/OwWq1SlrjdbsF1qCNXye7qF9doNAVSPP68Kq7gxlQxH2Zs6sJTsReV4M/Fx5/j3zOT5CJSHwZLX5ZtvFSKjhrg1YWpvufCwoLAHVQnqaUhACmBeQ/YpSZ2a7VaCyACBnxuME48ID+TgVWj0WBqagqZTAYWS97NrLKyEqtWrZKskkZGHHbJOW9cuHzf6elpCdLT09MwmUwim6atJn+eWDaZKAsLC9izZzfuv/8BoTOxKcXGjNPpxO7du4UrHIvFRJFEE6POzrMXXb8PP/xRgYocDoeYiOv1evh8PhnlQ9zvoYceLuDiZjIZDA4Onj/cGpHNZvHA/fdL1sX7ydKXajIqF0lds9lsea+N814cRUVFePTRx6QyiUajKC4uRn+/Cddcc400cq1WqzATysrK8KEP3fZn3/HQocPC21Wl9tlsFjtvvfWi9+a9rgNvvSVrmuU5qwI2hNk74GQJvV6Pb3zjG/jZz36GaDQq+02n0yEUCuHUyVNYsXKF7Euuf1YHKo1u37592Lp1K37zm9/g0UcfFRe7TCYDs9mMjo4ODA8PIxQK4dy5c2KPSe+Mqqoq8Y7gOClWHKwYuYaYrPGgUjNdVr1Mxvjsn3vuOej1ejidTjFFWgplXuy6ZNA1GAyIRqNob2/H0NCQLHZ2rZm1eTweHDlyBHV1dUgkEjh+/DgeeeQRtLe345vf/CYymYwETXZI+QV5Mdjw92o6H4/Hhf71bj//bpdKB1E5ueql/tnS97/U613sddTXe6/3W/pnDLh8H/KG1TJxYWFBGjkUBHBkeTweRyAQEEjG6XRiamoKDodDiO/sxhJ7LikpQUNDA1wuF4qK8lN0JyYmBDphsB0ZGcGyZcukgwsAoVBIOrlFRUVC5mcZSw9WZrHseCeTSbz55j75/v/xH7+U31911dXyvHw+XwEDoLe3F3/1V3+FqqoqXH/99bj1fEBxu6uh1+tRXl4uWBozwdnZvFJtamoKIyMjwmXmxrriiivQc75BqtPpZIJzMBiE0+lEKpXCunXrxGSGLAer1SqYdn9/PxoaGqQhOTU1JVN+jUYjLBYLli1bhtHRURgMBrFLTCaTmJiYkIOO71tTUyNZHQ9YMm4+8IEbBRabnp7GPffcg8FBnTB1SHtkgnPk8GHodLqCMfQAsHv3Hvh8PgwPD6O+vh6///3vsXLlShw/fhy1tbVoamrCFecDbDabhV6vRyqVQmdnJ44dOwaNRoNHH31UmrbsHfT19eGJJ56QCmdhYQFut1vcwQAIBq5eHo9Hkg6bzYa/+7u/ww033IBf/epXOHXqFCwWi6xdJjuJRELUZrOzs7BarXC5XOjo6EBnZydOnTqF1atXy3NRZcyUUvP3VNlOTU0JMyKVSmF6ehqzs7MyMojNTlYB7xYnOCXnUtcl4YWdO3fmwuEw6uvrodPpZADftm3b4HA48F//9V/IZrMYHR3F4uIi7HY7tmzZApPJhNtvvx3xeByPPfaYuEDZ7XbJoliSq0GHN54YFcsYAAK4s+Rn9lZWViadXTVbZpbJzA+4gEUt/QX8uTcn/8sseOnF11dhj3e7VHEGDxp+L/WXyuxglstyXCViO51O7NixAwDEFlCn02F6ehp9fX0Fpu0MYKlUSqwtS0tLRXvf0dEhmHRPT08Bp5EZMg29jx498q7f79ZbPyhNicrKSkxMTAg+S+bEwsICqqurUVxcLH/O0Tkk3FssFmG+2O12GAwGnDp1Ci6XC3q9HoODgwXVDTNwGi7Z7XZp9KXTaSxbtgwaTd5D1uVySYnZ1dUljlSVlZVwu93isxsKhYTy5na70d3djYaGBiQSCfT392PFihWSpVutVsnQk8mkWEN6PB6MjIwUwF3ETauqqhAMBlFdXY3+/n5RcKkNJr1ej2uuuQaRSEQ+Oycmc+2rPhzZbBZXXnklioqK0NjYiObmZtlDvL+5XE4OZIpXxsfH4XQ6cfToURw7dgx6vR5DQ0NoaGjA/Pw8fvMuBt6f+cxn8W/fydu13nX3XTAYDPjyl7+Mrq4uZDIZfPWrX8V//ud/isqUWWF5eTmOHDmCb3zjG7L3qWrU6/VYuXKlmDulUilEo1FpeFHgweBJjJoufWT/MH7QT5t7mpUrcVuV46xiy2oMYkXNOLQU2lyKqavxgz/39ttvXx6me9NNN+WmpqbQ0tKC119/XewSp6enUVxcjNraWhw5ckRwEFXP/fjjj2PdunV48MEHYTKZEAqFJAOJRCLS8VTpGzSaYPAkyM0silgMWRGLi4vw+/0Frvvyxc6zE9RMUSVcqwFXvVnc1PyzpYFXvflLH4gKEahULx4gajlKIxuVH8zXstlsYhnJzJEX8V9+x1OnTr7rs9u0aXMBrs1mYSaTgV6vRyKREG9ZlrxsJDFYkgI3MTGBYDAoyixCAXSAYkNEp9PB7/djfHxc3pNiDkrGGxsbCyYUazQa6aLr9Xo0NzeLuObkyZNwOByorq6WQMH312g0Eozcbjfm5uZQXl4uG5JZ7fT0tDBeTCYTwuGw9BkcDodYPy4sXJjjRe4rRS00MXI4HIjFYsJlHRwcxI4dO4SpUF1dja6uLqn4aLrf2NiIsbExmM1mGa6aSqUQCASQyWQwOjoKu90OAOjo6AAArF+/HsPDw2I2xDJeq9UiGo1K5sfymYyObDaLu+++GzqdDiaTSUr38fFxgQUcDgey2Syee+458Q3gWpmamoLH45FpEuvWrYNOp8OePXuE3TI0NITKykpMTk7i85//PJ599lnU19djbm4Ofr8fH/jAB7Bx40ZhTczMzGBwcBDf/OY3Bevns+L6VJt16t6h5SYPEe4BrgFW29x/qmOY2ithYqD+HamUbBZS5KTuY/Vn1dhysYux4MSJE5eH6dbU1CCZTCIQCAh4PDExgVAohLq6OnFjIs+W5RWzut/+9reiWuFGJViuKmj4RWgWwUCTTCYRiUQkmDOzZVOIN6i8vLwAJwIgJcRSCIFZAH//btAATy1mmZyYClzwjl0adEPBEKocVQUlkGquof6bQMD/rvd79eo25HJ5kxBSXHgQqRgYcMEBrq6uXrL+jo4ObNiw4byK7SbU1taeD8x5ccXw8DB6enoEGx4dHRX5KFVxXOTJZFIqEzYzyXdmVUH5MTNtIE/C5/y7ubk5vPPOCaxZsxZNTU2S6U1NTUkACQQCcmi73W64XC6h1LndbgmCAArwYBrKRCIR4QJPTExIJ57sGIPBAL/fL3i0yWTC+Pg47HY7otGo8FUrKysxODgo8mCr1YqpqSnYbDZhcfCwZKN3+/btImCpr68XTwZi2VarVUZbaTQagRFSqZQ0xLjRi4uLhZ/LLB6AQHJnz56Fy+VCTU0N7HY7fD6fWFmq5kKzs7P48Y9/jM9+9rMYGBgQAQ8Vn2VlZbjhhhuwd+9emEwm9Pf3CwRjNptFmdfU1ITi4mLs3r1bjNkTiQSGh4dhMBhgsViwdu1aHDt2TBzYaE36yiuvYGhoCPfccw+AfM+AzBhmvmSccB1zT3J/8XBl7yeZTAoPemmixmtp/4T2BWrTnHuQEAH3OrPypdAH/07NatXqfOmfvZ/rkkGXks2enh4sX75ciNfUMB85ckR05xRIWK1W3HzzzWhqasLQ0JD4YhJLVOWflKUyIwIgfrgs80wmU4EfJpViQD6oOZ1OAChI95nJ0IxYpdaol/pv3u2msTGllvwsGRnUCfADQCgYQkNjg3xnZg/MCkOh0Hkc1iyBLpvNj1vn5AM2KhjgfD6fiAuYkfKeabV5gx0gLx299tprodPpMDMzgy1btuCLX/wiKisrce+992JhYQHLli1DR0cHfvGLX6C2thZjY2PSTMvlcrBYLFI2M/uhfyyn3jJTUB2oKioqYDQa0dDQgGAwiHQ6jX373sBdd92NurpboNcvx8LCAsLhcIGIhRgw7wHZGm63Gx0dHfL+PGSdTqcE2JaWFrz11lvQ6/UySdpisUigjUajgvHq9Xqxcezq6kJFRYWwbyKRiPAwVRPseDyOiooKDA0NYWpqCna7XSwW2bTkRIqqqiqMjIyIuX0ymcTw8LCwTrgW1EprYWFBuM+k7cViMQwNDcHr9WLdunUoLy/H8ePH0draiqqqKpSWlmJiYkLM+S0WC1wul/hyzM3Nwel0YmhoCK+++qpUlAaDAZ2dnbDZbNixYwcmJiZwyy23IBwOo7OzU/YGGTeEdlpaWgR6am1tRUlJCQ4ePIhMJj+3ze12IxQK4ezZfDOT43FuuOEGvPHGG1Jpfu5znxNuLVV1dAoj22fpvmRVSMiLzm6URl/sWgoxAn8+uVtlMan7XqWCqa+h/rx6vVcP52LXJeGFBx98MDcwMCCbgrSb4uL82BMSxw8ePChWiC6XCyMjI8hms7IAqIChNR1noHHx5XI5OBwOeDwejI2NCfeSGVQkEoHT6RT3JgbwpTcUyDdRpqamxBmIGbbKkOBDXXqDl14qU4K/crn8pILq6moMDw/DYrGgoqIC/f39YqNI2ICTFQBI+c7yl81EnsJ1dXXyfgzaDPjk2pJ/qDqwUc3jdrthMplw6tQpVFdX44knnhAGgc/nk7E0VqsVZrMZo6OjOHDggAR1i8UiHhoMLiz/iIsx60qn03C73fIZGxsbxSavvLwcb7zxBtasWYNUKoWRkRHU19djaGgIRqMRq1evRiQSQW9vr1QC7IJzcoLb7YbH44FGoxGslVXVY489hp6eHuzfvx8DAwNCmTMYDOLYRRqayqskJsqM32QyQafToaqqSihaTU1NKCkpEaMbPi+yHDiXj5NUmFGr2CNVUuFwGDMzM9iwYQOAQrvDRCIhY3zYpKPMl4dofX293DufzyfPZ3JyUuhKxcXFcojRGMnj8Yjz2OLiolC4VAz43nvvxcDAAK688kq8/fbbeOmll1BfX4/5+XkZ+VRcXCyvl06nUVdXh5KSEvT09IjYp6WlBTMzMzh79qwM/+RYJh5s3//+94WB8fjjj0s/g89EVW9x/b8bXkqKnJpwEC6gbwh7FrxUOIGV7dKgq14qtsufZaJGzvjSiph7cGlw7unpuTxMd/Pmzbnp6WnB4Ww2m1jgcQikqu5iV7W2thajo6PS9aPAgkHD6XRicHBQPjgJzOT8EkLIZrNCq2EAYjnFU5TBq6uzC80tzZibm4PX6wWQz5rJeqAfaklJiUgPSQth4C4pKZGpA0tPQFW4QU07D6OWlhZoNBq89dZb4kjGRuFS6zjSURhwOdlAq9UWwCGsBMjzVRVy3OSsGKqrq2E2m4V5UFZWBrPZDIvFgg0bNqCsrAxerxdutxunT58WDrTX68X8/Dy+9KUvYfXq1QDy3VdyGFV/WVX5xHLYYrGI/+v//t/fxGOPPY7y8nK88MILcLlcqKurw4EDB+D1ehEIBFBeXo7ly5fDbrcjEAiI4xdHw/Og1Gg0qKmpgcPhQG9vr5hYz83NYfXq1WhubsayZcuwefNmXHnllVhYWBCNP6EKjgeqrq5GZWWlGL6oqriFhQXcdNNNOHbsmJSXFOOoz5dMAp1Oh8HBQdTU1KCqqkqgF9VTldMZOOSxvr6+gCJpMBgwMjIihy3namm1Wni9XpjNZmEhEEKjOIncbPpMMzgw8LOxR9/i6elphEIhTE1NyT6orq5GNpvF2rVrYTKZUFFRgVdeeQULCwuiPiP8lMlkxPBKnRJDLwl6HAwPD6OtrQ1dXV2yx/nZdu7cia1bt8JgMOChhx5CeXk5xsbGYLPZZC/yIlRHVRybvFSaqT0alQpKfrjRaCzYt8ymmTUvbZ5f7Hq3DJb7kIpE7u2ln4VBOhwOXx6mG4vFJHNoaGgo6H739PRI0HC73VhczBtRsOPd0tKCvXv34uyZs9i4aaPMA1PL7XQ6jeNvH0fTLU1SvjFgZbNZJBIJvHPiHdxx5x04c+YM6uvrJQuzWCyIRqOC1TFgtLa2QqPRiNkwAx0t8TKZDM6cvTi38396eYuKMDIygpqaGrk3DLhLLz58bjhKZxOJBLLZLOrP6/iJVS1txPF0z2QyMBgMmJychMeTd+mlkIEcXZfLBbfbjR/96Edob2/H/Py8zPlic4VTc1etWiXD++ifwQXEDeD3+6XjzyBQVlaG8fFx/PjHP8Jjjz0u5jOcjhAOh3H77bfD7/ejqalJhDHMBurr6zE7OyubhRhxNBoVAxy73S4ZMQd+lpWV4cyZM9i6dSvefPNNbNy4EePj41Ix0cODwpDJyUmYzWYJcNXV1YjFYkilUnjxxRdlGjPXvM1mQ3l5ObxeL4aHh1FdXS2jlmiN2dvbK+IgZtP05qX5EbNeNrsIf/DQUSfzFhUVyffmvz18+DDMZjM2b94Mj8eDWCyGaDQKn8+HlpYWxONxEcUwYSB+evToUbS2tiIej2N6eloyxGQyKeKXkZERSWiCwaDw6oF8sy0QCAgkFAwGceONNyIajSKRSGB6ehp1dXU4fvw42tra0NvbK1XY2NiY9ICOHDmC5cuXSzClWpN0R2LXACTxYPXo9/ulebm4uCjNQgoXKJrg4bAUe6VRl9rD4XWpCpeBdGlznZUP/616APA+vhtc8mevf6lMt6GhIbd69WrMzs7C7/dj+/btmJ6eRjqdxuDg4Pua1qDJAW1r2mAymUTTbrPZcO7cOaxatQq7X92Nbdu3wWw2448vvvhn/xbAJZ3K/p+6jAaDZJikIhGYp+0b530RgG9vb8eZM2ckK1ZhEOCCkTQ9HwAIhsYsgsZBAOT9mD3QKY2Zp8PhKJjUwM+YSqXQ1NSEQCCA19944y96X66/7jrJPvbte0MoYzxMaKfIyiabzcud2VW2Wq3IZDIFY02I9dNbgP/PYMINwC4zrf2uuuoqgRKefvppIa+zR1BSUiKqR41Gg97eXlitVhnCGA6HJdA7HA6BwHiQESpjs5ZYJD8HZewzMzOCAVutVkxMTKC2tlYMf0KhEFasWAG/3y+4fCqVErc4BgryjRlIBwYGhDGg1+vR2toKn88n/FSyH9h4VpkvrHxoBBMOh7Fy5Uo0NjYiHA7DbreLExirQHXUFqsPZqI6nQ5PPvkk9uzZIzxvNi8zmQyi0aiYKIXDYRQXF6OlpQW33XabVFF/8zd/I3Qtig1UCG5xcREulwvz8/MwmUzCXSabgU1Sv99f0LDm3uJrcK1RMKJWxGrzXG2q8/+XZsEqbLC0caa6KppMJqTTaZnX19vbe3nwwlVXXZWjimNhYQEtLS2CZQYCAYyNjUGr1eKmm25COByWk9RoNCIUCiEYDIpU0el0nneMyoovAU9f6tbZteVNprEKkD9VpqenRUlCYxM22IqKitDd3V1AA2IZz8XFki0UCskBQGw5dh5W+EtdzvN0I+CCsk3lDnOBq39eVFSEcCTyF/0c7+f62U9/il27dqG7u1uqCHIaaaRSXFwsEBAPI+Jn/HMuTtKb2CRTlXKkBzE48lDiwcL3Y7OWjVDCJoQMSGA3mUwwmUzYv38/+gcG/qL3peOKKwomy5JH6/P54Ha7xcKQSQmbwITicrmc7Ali4TwYyIxhHwCAQAzZbBbvvPMOstm8eVB9fT0SiQTm5uZQX18vsnjiozMzM3C73YjH49BqtTKwlVCc1+vF6dOn0dbWhhMnTsBsNov0OR6PS4ZrtVrFiY3QWzZ7wZv2yiuvFCw+Ho8jGAzC4/EU2EvysDSbzSgtLcXy5ctRUlKCDRs24Fvf+pbg2AsLC6isrEQ0GpX7rdVq0d7ejlAohOnpaVgsFiQSCZSU5CdXUFxCae9SaAK40Ayrra1Ff3+/9DWAQoUpqwuKK1QIUW24q6woYri0oyXURkaGw+FAPB7H3NwcxsfHLy/ofuELXxDvBZUFQBJ8fX29zGXiogyFQjCZTFi5ciWi0Siy2azMjCdHNJfL2/hZrVaZvkD+I8trZmy8KcT0uNAZQBl8eXJ++9vflgARjUYRi8XE+IVZBT1HR0dHMTs7KyUuAMnQVMI1NegsZ9QGG7Fhld6l0WgE71EftirE4N+rDvxsjPBzsDlz5ZVXSnYyOjpaQH8hB9VisQhWmc3m/VHtdjtGRkZgNpvh9/tl0ZSWlmLdunVYs2aNNKhuvPFGWK1W/Ou//itOnjwJi8VS0MDgJGL+P58RHav+6/e/v+g6upzr5ptuknWSzWbluTAg+/1+1NbWorm5GQ6HA3V1dfjc5z6HhYUFjI2P/0U/y6WuW26+WRzZeNCQckWqWSKRKGDAmM1mgQJIp2TWxX+zf/9+aQZWVVVJ0sMZYBwDRC4rAHF7CwQCiMVi0p8grMGGNimYXMfl5eV48aWX/qL35f777pMBjnV1dejv7xfuNhuBpaWlBdzrlpYWFBcXY2hoCBaLRQ6VdDoNl8uFvr4+WCwW8aSoqKhAPB5HVVVVfqxVbx8aGhtkD7S1tWFqako41FTGMemhZwVFOfwzNjMpIddoNMKISiaTQlXlRSYSWU1FRUWYnJy8PEzX6/VKUGEZR+yqrq5Oshbqnen+Mzc3h76+PslUmbm63W5R5JDkrdVesLpjWakG3pmZGbkJLLUGBgbEjZ8qJwbHe++9F2+99RYOHjwIh8MBt9uNWCwm/5Z0KLr3898R7+Wl0lX4i6UJm1klJSWS4dMhS9XA8/Tk6b+0tKHyRW0UMsAAkGDc1taG1157rYC5QQyLGUlVVRV8Pp9YGBYVFQlORyFGNpsVg51UKoU333wTTU1N8llTqRSeeOIJ7Nq1C6+99hpCoZBgh2rFwfKQgaCsrAz33nMPFhbyM6noZaDX68WPgPQgbiLeA/KSQ6GQeMtmMhnoR0bQ0tIiuHP9eZMelrw9PT3C0zUYDHj55ZclM2+orxe6IYdHstwdHx8XJgDx26GhIRnr4/f7xboRgMhugTzOyZlt7Au8vGvXu2+e/2Hgv/+++xAMBgWD5uFdWloqXGKVYkb3vvn5eXzv+9//H73XX/ravGmTyH3pQxwNhxGNRtHS0iK+F8/97Dlc0XFFgbE4ezudZzux47EdSCaTcDqdAgtMT0/j+NvHsXbdWoHe6CIYDAalalL5utXV1fD5fNJApH8GJ4rr9XrEYjFptNGEfnFxEQaDQeIFD1L2SVilEgZjjOL6YrNb9Q1+t+uSme4nPvGJHClO3NwMIqSs8AOwLGTgYqCh5SGny5IBwQVF1RBPHwZy4nEM5FVVVWJSTHwYKKRyMAstLi5Ge3s7nnnmGfncxcXF0thg2UsOKHmgDO5s5DHoLi4uSoauupgxo1Q9SImvcSHw9dWgS2YCWR0qpcVisWDVqlU4deoUysvL4Xa7RVTQ2tqKo0ePwuVySSOmvb0dPp8Ps7OzqKurw+DgIHK5nNwregoAkCBETi7FL2azGTfddJOYRMfjcQwODuInP/kJuro6sXbtOpjNZllolJLqdDo0NjZicTGvYad0lRJQr9crbA9m+2wWUVQRi8XQ09Mj5TYbWmy2qlmQatgTDAbR2Ngoz2rTpk34wQ9+gImJCVitVjidTkxOTorKqrm5GVarFUC+WWYymVBSUoKRkRERX/A5s+ri1AxabpIzzs9KA3+TyQSfzycKLK5x4EKiwMYeZcpUVdLlDIA8m2AwKCOYmGmNjY3BYDDA6/WKgRT3IHFrMlImJyfFO4OCDiYWZB/wz1jFajQaGcNjtVpx8uRJEZNUVFTglVcLTeIvHlCAhx5+SBSAJ0+exM6dO3Hs2DG8/NLLWN22uqAqi8Vi6OvtAwBsvmKzVJWhUAhOpxOv7XkNAFDrrYXX60UmkxH/iq1bt6K7u1tkv6zEBwcHZXoGXfTIPGlqahLDqdraWrElOH36tCjPWCkzESHTqLKyElNTU1Khcs2odgWEHC470x0eHhbHJ3aGqRgjYX9sbEzGWaTTaVgsFnnor/zpFey4dodM4aTVIMssZrSZTAav7XntfTXM2Fy78647JUNj5rTr5XzWsWLlCjFI5pgYg8GA0dFRwbJowq6W/GQ9AIWg+lKydC6Xn4wciUTEGUmFRwo+r6bQ3Ux9DdWWknxDvV4Pv98vB5lGc2HicW9vr8Ata9aswdGjR8V9LJ1Oo7u7W0j/POxsNhvS6bRkDeTb0hSbs63Y4GN3vrm5GQ8//DB+//stiMXyZTODABenXq/H2NiYlLVut1umIdCDgdg8m4002uFr5HI5eL1e+P1+EYewFCY2CEDYKGRhuFwuLCwsyIgkGq7X19eLfwMPSLfbLRjs0NCQ9CjIeWXzhY1ANk8ZlCiAYRBnRs1slKb+AIT9QeN3VR7vcDjEMKW2tlYEBslkUlSdnHRBeM3pdGJ2dhaNjY2S5NTX1yMYDMphb7VaMTIyglQqJVMUampqJCujvJvPIBgMIpvNwuVyQaPRSGbNXgwPv7q6OgQCASQSCdy6cyfC4bBkiNXV1fKsqqurUVVVhc7OTlR7RrG4I7+XwuEwtm/fjhMnTkCj0WB563JoSjTCMhC2yeY8PGivtYsIgrhqx5a8SCZeFhfuL78Ludq34AUAACAASURBVN8cBMBmrNvtLlDBBoNBmEwmmf7BHlAgEEAkEhHePAA5eNQmGz8r5deqaQ9jAxt77IVcOoZdItO9++67c6RnVFVViV6ap8HCwoJkWSSOM1tkNzMcDsPpdAoZmtgoMRSVBbB0CCUxVGJAer0ek5OTcLlciEajAgnQiYpad5bbR48elZtD7wOeshzzzsDAz8YbqRK1FxcX5bVZOvjPNxz+/3b95vnnJcDF43H09PSgvr4eb7/9tjAPeIBS/cashtQqWj2azWaZf8aFy8NndnZWpj3z4GX1EA6HJRPlfWezkZBTMplEa2sr0um0BMhrrrlGJlsQQhobG0MikRDhxrZt23Ds2DH88Y9/RDabhdfrFcyOLAubzYa+vj6ZaMCKQTWYZ5WmBjPS/qamprBy5UpotVoMDw/D5XJhYGAA4+PjMmfQZDIJTEC1HZ3HmOFzDh0PBpa4HPDJA6miogIulwu9vb2yD4qLizE5OSlZNYMHOcPMNJkRklc8NTWFiooK+Hw+mM1m2O12mM1m2Gw2bNy4UVSH5OuzAmRlSDELobFMJj8xpb6+HouLi+KXMTc3J7JjVXzk8/mEOsbnTaqcw+GQph4AcXtjo9ZsNoui8Ctf+QpaWloAAJFIRBpzpNkRe3U6ndBo8kMtU6kU2tvbMTAwUJAQMhawV8NKnLGBAVulj12Kp/uehjcs/bdt2yZlDK3mZmZmMDo6Kvxclb9GrIPdbAY/g8EgnD3ihKTtqFpspukM1mazWfBOZh484di4GBwchM1mw/j4OHw+H2w2m3A1WV6ppxZwQW3GZiAAuXHqL2ZeKg7LXwwI7FYTQmAZqsIRvD/sXAMXvHPprqXVarF+/XrU19ejr69POsVmsxkejwfHjh0TwUpRUZEMw2OTjAel2m0tLS0VHwJ+T5fLJeV7TU0N7rzzToEQhoaGkEwm0dTUBL/fj1deeUU2+vz8/F+civb/lmvb1q0C/yQSCZmnxsCjCmqY0agm+5SzE4PVaDTCD2aHv6QkP9PP5XKhuLhYoICWlhbMzs5ienoa4+Pj2LVrF5YvXy4BghUD/58eBk6nEwaDAVu3bsW3v/1tVFZWIpFIiIes0+kUh75wOCwuYIQejEYjqqqqZKoxTYKcTidaW1uxcuVKHD58GNu2bZMKl9grGQTkcLO/YTQaMTs7i0QiIbxvipX0er2MxWlsbJRmuiq75oFL57epqSk0NTUJU4B0ObqicU+xAZ3L5bB//34cP35cDlIexDyE6LxGv5hUKoWVK1dibGysgMnAX9zHhIXUoKtSQPV6/eVTxtavX5/jzW1vb5eFVl5eLs2rvr4+CTqqrFWlXeRyF0baqEwAgtg8mdkYI72LJxMzJaPRCJ/PJ16nZrNZGkfMshcWFmA0GmXa6aFDh3DHHXecn7vVh4mJCQHOyblU/QzYzFMDKoMuaU680czAyaLgw1gadHmxnGbGx6DLwY7E/mZmZrBy5UppHLAhcfbsWYTDYaRSKely8zRmJkMeKHEvfn4ebFqtVviRWq0WdXV12LXrZVx77XV44oknBO/q7u6GzWbD22+/jVWrVkkZun//fnkNNuX4/fl9yNMl1qVWLPwc6uhqlsE8FLmJVYkl4R8Gj1gshmXLlskmo1yZI8XVJimVWuFwGFarVTJ2loOkIQEQxdnk5KTgnG63W7rsRqNR1uPe11//H4b0/29cmzZuhNPpxNzcHEZGRoQ7y8qGijsmUuRWEz4hj5ZZMJuuJSUlMh6L4hpWC4TJ/rTrT9i2fZvEk4qKioK9XVRUhHg8DrfbLaIi8qY5jYUDEHgwvfrqq8IsIk5PeI1casJ2fObkPb9b0GVlxPdWecBMPEOh0OVhurzRNCOhhRx5an19eQCcggF+KDYHVONynogsTYG8gkyv14v8lXgaAwjxF2JsZA1wtjy5j8w4qBsnbsYSr7Ozs8Dtv66uTgZoEpvkicoGCL8XNy6zRgZNLj4VDnmvS2VEqBQ8fhez2SzZciKREHm0VqvF5OQkIpGIBEsagqtCC5Z0LCP5mXkYkifNCoKb4gMfuBGp1JxAQ1QZ7tu3D4uLi1i+fDkqKipQX1+P3t5eOTBUyhFxVQY8YqR85sTidDod+vv7ZeFXVVUJTMTXo1CksrJSskqOlydvFshzMdmEU53pyCNmubf71d1Yv2G9yGy7uroEz6c3AjdNfX09xsbG0NraKljoxMQE6urqZHxURUUFKisr8eHbbxd47MyZM9i4cSP8fr88U7qeud1uDA8Po6mpCf39/XA6ncjlcuJxnMvl0NTUhI985CNiQENIbnR0FH6/H9PT00LdGxgYQDKZRENDAwCInzXH0ZAeyEOfJT3XLSE2j8eDiYkJwcAHBgZQVFSEqvOwIZCHLlgpcV8zO49EItjz2mvva+2/595ATn731v633ld/58YPfABarRYWi0VYSXNzc9izew+WNS8TnjDn5TH5GBkZgdFolIkeBoNBICPVqOpiCakKP/LinmaCcMnveqlMd926dTlmoIz2LNP5e4LuXOB6vV7KKpaxJGovLi4KwZtSQBWjGR8fl7lFWq1W/EB5IzQajejZSZ3homUWzrJ95cqVQglZs2aNGDXTxm52dhaRSERO5crKSulWErMh7qXyVdl952cm95f3URURsOkCXODosvQhSZwPS6/XQ6u9YK24c+dOURMxq4hEIojH4+LvysOImRdhHGbdzHxJkmdTYeXKlWhtbYXFYkFXVxe2b9+OM2fOYN++fdiwYQMef/xxyfqDwaCogAYGBmSuGKsMatGnpqbQ0dGBkydPwmg0wmAwSAkWCATkuwCQBpTX60VlZSVsNhsGBgZgNpsRjUaRTqfFAGlxcREDAwMoKSnB4OCgcCSj0Si8Xi9sNpsEZQBictTa2oqf/vQn6OjYgsOHDqN1Rb5MzmQy8Pl8yGQy8Hq9KC0tlfu5uLgoLl5ut1uYOVqtFj09PXC5XHA4HAW2lslkUhrHnPzs9XqFLllUlPeDZjNzZmYGXq8XkUgEPp8PxcX5eXm1tbW45ZZbEIvFcPDgQTGtoSCgt7cXuVwOIyMjwuSx2+0Ih8PQ6/WSqaksH8q6Vce+0tJSuN1unD17VuTgxNPJnNi8ebPsXbfbLZASm9Czs7Po6+tDKpXC8uXL5V7woPf5fELn4v5msnDo0EHcdNPN0jhOJpPCxqFAiIZVTLjomUGmisFgwPj4OG677Ta8/PLL0sQ3GAwIhUKCdS8sLKCtrQ0/+clPxHGOTA3e94mJCXlfJiSEQZi5MtapPjLqGHYmXyq7a2Ji4vLghe3bt+dYMtO2ji9KSg0VKywxGWj4IbRaraTtqtqIQYLldzabl3wGAgGhgGm1+amktbW1MnOJjvGcQEBDDp7qWq0WIyMjqK2tleBz5swZNDc3Y8WKFWhubpaGmRqcVB9P0khCoZBQs7hobDYbzGYzRkZGEAqFJMsbHByUjIPZMBcocIGxAEDKIX53rTY/CVWj0Yi+/PrrrxfOL30DiHGRjK9KbYk3q9pvdvRpOm+1WvHRj34UOp0Ob7/9tlQvLAXD4TDOnTsHo9GIHTt2CHGcv0pKSiQw8jDUaDRi/tLU1IQDBw5Ip97lcgnH98yZM2IFyM9it9tRW1sr44TGx8eRSCTkYOQQyenpaQwPD8s9Y8n6/e9/X0ba9PX14dSpU3j++V9j+/ar4PP54PF4EIlEsHz5csmY+RxYMbFpp0qPKccOBAJwOBwClwwODqK2tlYaMazeVqxYgf7+fng8HmG27N+/Hw6HAwAki9fpdPB6veLYV1lZKTzv8vJyrFixAgcPHhRPDsIufr8fTqcTXV1dokxj55+c1s7OTtTV1SGZTIqqk7adqlyZwVFlXLDZxUx+9erV4pkbiUQks7dYLAIzjY6OIp1OY8eOHRJsFhfzvgg9PT0oLi7GW2/tx7Zt22WtMjlaak3K9yVExMqXQa6oqAihUAgOh0NwaJqp22w2gblisRiam5tx6NAhzM7OwuPxyMiqY8eOSSZKH2JW1+peZEOUQZeBlRUx6ZeEQtWgy31+vhl8efACZbfRaFSCCbEZYrMMWiquq3aaidkxiySdiYGWXEcGcRKUSVAnBYPaZgYznU4nJZDL5ZJFtXz5csRiMen0lpSU4PSp0zilOX2prwoAeP7XvwYA6arr9Xo0NTUJx4/gfTgcls/Y0tICk8mErVu3SlYbCASwf/9+0aBzw/PzMzNVDXCWYshGoxHBYFDcuWZnZzE2NlagglO132pnlQscAOLxOLZv346f//w5/Pznv8CePXtgsVhw4MABlJaWoqmpCalUCmNjY3j44YdRVVWFkydPoqurS/DrUCiEhoYGlJaWCswRi8Xg8XiEQmY2m/H666/DaDRKczMSiUhGTDN7IK/08Xg8AqOQMkXuNZssJSX54Y7pdFqMUdhtByCQht1ux91334UDBw7iM585AK32Xwrwd95/bmbeZ1ZjpAmqHWmuu6UbilkugILX5uZjk+fzn/+83BsaExEqUiWnNIsny+eWW24RabrP58MLL7yAuro6+P1+2Gw2hMNhXH/99Th06JBMoiCkFAwGBeabn5+X/cv3YzOL1RT3MwUAFEDxs4VCIRiNRmi1WqmwaOnJ9UVq4B//+N945JFHxfhGo9Fg69ZtKCrKByYqCwnpMU4EAgEJVJRUMw6Q4VJSUiL9HQotyNPmfuA+6ezsREVFBcxmM6xWKywWi6gmt23bhlAoJMwLrlvuU7W/wCa9CgcyHqlQoyp4ej/QAvA+4IWqqioMDQ1JyU2zEzZQ6GRPqk8mkxFCNdVAGo1GMFDihqRwsQRmpnHu3DkYDAax+SMWS+FAIpEQy0Hy7+i/wIyCi48GzsTcOjo6cPz4cdx3333SKWYpQdwrnU4jkUhgYGBATsO5uTn4fD4JknwwVC3xBKWTGr9fNpsVT4mrrrpKsrfFxfyYoXg8juHhYVG88PABgCeffBJjY2P46te+9p4P8f1cTz/1FIxGI6LRKAKBAHbt2oWioiJs374di4uLsFqt2L17N3bs2CFMke7ubjGov/rqq/Hiiy+ioqJCAg2zQFL5Fhbys8MikQiqqqqkyakGt3PnzsFut6Ojo0OCKZkoPT090lBkeWo0GjE6Oiplvxowe3vP4c4775IGCaEdVma8nzwIuIFLS0tl85L+yPWpYuFckwyWxKjVn1MVUeT70ptAzcYcDkcBtYg4diaTH9dz+PBh3HbbbZiZmcHbb78tPzM1NSUHXCwWg1arhd1ux8mTJ5FMJrF9+3YEg0G8+OKLEhii0ajsF/J9eeCwoiM7SFVZUSC0cuXKgmSA35nV6J49u7Fp02bx3+Dz5T7mwcLnxAClMpay2bw9gNFoLJDnc7/xNXi/CM+w6lCbcOp3UvFYGru3t7fjhRdeQEtLC0ZHR2GxWNDY2IiamhosLi6KrzTtMnkw8RmqzXDgwtBYNfCqeO55973Lgxd27tyZy+VyUt5zsel0+XlC5M4SVyK2xc1BvX4gEBDooby8HDU1NWK8rJ5qpJeQLkKBADv61I5XVVUJBsuHyuyRWZ7P50MsFoPT6cTWrVuxd+9eNDU1ob6+HpOTk1KSEFdyOByIRqOora2V7JsPdnp6Gk6nU0q+oqKiAktJwhTEk5mFBgIBdHZ2ytBEHjK0HqQqi4yQqakpLCwsIBQKycZhJlBUVFTgTzw+Pi4HoFpd8Broz5u/rFq9CuvXr0dbWxtGRkbEZ7WzsxPr16/HoUOHsGXLFqlmAAjORSiBHGaPxyMDBGnXqB52PCDXr1+PyclJAJDJExqNRsrMVCqFDRs2oKgoPzXYaDTi9OnTWLZsGU6fPi00OH5vOqjt3fsa1qxZK7Q4i8UibIJXd+9+75PnPS4Ncuju7pFNBVzwTKWog1kuAwh/lpuOjl7xeFzG3PBneGBzzff19eFnP/uZ9Dq4prl+yA+nZWkmk5FJF52dnQLVAZDmJs1smMUSv2QQUTmno6OjcnjytchW2r37Vdx1193CGEmlUqLo0+l0GD8vc6bUlirATCYjAgp6Y5M5wIOJf1dUVCT+wQyc/Dn6tjA+cR0AEBFHe3s7BgcHMTk5CYvFIv4IsVgMkUgEbW1t0Gg0aGhogMFgwN69e4WGeccdd0gTnpO00+k0/H4/iouL4ff75QBhNcDnTYaDiveqFZBGo7kke+E9gy6QlyeqJGjKA/v6+mC1WgXIZrDjlydWyK4rA2tpaSlaWlpE8UYskim/KlskQA7kOb7xeBzr1q2TEpvZA5sBpCVxcKDVakVHRweMRiOGz8934msSmPf5fFixYgVsNhvq6uoKJM0qt5ZQAYny6inOi8wB9ffqA+HFAKxCBFyU6XQaL7zwglCdyPKgJnxxcRGjo6MF5Q4DFC+Wa2VlZbDb7aKgs9vtQilLp9PYuHEjjh07hsrKSjQ0NECjyZt6jI2NwePxIB6PS8OMTa9MJoOzZ8+itbUVLpcLLS0taGxslE5+/XmBAC32aLo9MDAAo9GI3t5eBAIB2O12OaSCwaA0NnO5nGRN3d1dWLeuHdXV1bLpQqEQfD4fvv71rwvnkwEqk8nIDLKlky9YeRkMBmlkkT7ICodMG5a+5KLSGJ7VDd+LJXAkEsFnPvMZqfRYSfG5MPixlP/lL3+JZcuW4c0334TP55MkJZvNyqQQei68sW8f/lLX2jVrBHYjH5Uz+zZs2AiTyQSDwSCc4mXLlqGvrw/ZbFb4tjqdTkzVyZ4hh5hydHpiMBNV9wsbwGVlZULf4xqj4otSaGLvdXV1KC8vF1UlYU7uD7fbLfRAun1x4gxht/HxcaxduxZtbW2w2Ww4fvy4NDX9fr88m7KyMqTTaZG1s2qmXJzPVD1AGIAZw4LB4OWLIwAIqKy68DgcDimdOOGUGJXKm+Qp4HA4ChYplSbBYBANDQ0IhULC4yV9g5JVBjF6eDY3N8t78aEyM2U22tfXh7q6OszOzmJgYAANDQ1oa2uTrDmRSEhJPD09jc2bN6Onpwdr166F3W6XyRhkHTBoskxT1SfMHlReLP+d2rghRsTMWP0Zvk5RUd4U/U9/+pNkripFjpkP8WUGbf6sShFj8zObzU83sNlsealmdTUWFhZwxx13yPTe1atXY9++fZJFUvtvMpkQCATgcrkwPj4uzR8AovJhx37ZsmUF1BlmMUVFRTLji4GH64ZG+TMzMzLT64tf/D/x0EMPF3h+0AwGgNACydemWIcNJB6OzEZJCVJhAF78OdVrgbaRPDT4/GnuRLaAVps3M6+pqcGNN96IhYUF6earXss8GFndfeMb38C1116Lo0ePioKPcJQ6GULFncmI4bPkgU18W22qVlRUyH3l9+K6OHHiOABgzZq1ACB0K2a5Go0GRqOxIJg2NTUJQ4VURlaUPCSy2WxBs5kBk807yqKZENFKQKPR4A///d+XOife93XXnXdKU2z9+vU4fPhwAc/+wIEDGB8bxwdv+yDuueceTExMSGOc91ZNtlQYNZlMIp1OIxaLSWA3Go0idmHz98CBAzh8+DCGh4cvL+hed911OTr38+HyJOdoZXLjePIEg0EJKCrWyUXBSa9sRHFSKb1A+UWpwOF/2VEsLi4W4QADld/vFyrR/Py8DPf7+XM/x5133YmmpibMzc0Jd5j+Bnq9HidPnsTKlSsLBk0WFRXhox/9qNDUMpmMyFGZkcoN1FyYIMrApwZlBkTeZxWr4r9XAXie3D/84Q/lEGGgJvZJriE77Wqjh4tEpcXQ2o/3eNOmTXjkkUfw+uuv45133kE8HsdNN92ESCSCrq4uORQcDodMaqioqEBNTQ3Onj0r2CVhCG50zg6bn5+XqbYcTUO4gri8w+HAmjVrZHPec8/d+MMf/hulpaXSBFVZGexaj46OQqvNO9SdO3dOgi4XP+81n5nT6ZSKC4A0gDlV42Mf+xgefPAB/O53/ylVDO93LpcTKIufmTghg+rg4CC6urrQ1NQko3B4mKqQk1arxa9+9Sv4fD40NDTgiiuuwDPPPCMBld81FouJDSExTh7i7PqzyaXT6YTaRviOh1RFRQXeeCMv3rj++huEbsmsjXuHCRLvIYMu/QWIoRLO4WdhCU7fXABi8s57wLWpHoLxeFyYL+wTcZwTM+LZ2VmMj4+LmIKV5/T0NHK5nJjYV1VVCZTBJIHipGw2K4NgCU91dnbiXM85XH3N1eJfsnHjRnGYU6GMpVxcQkRM8AwGAyorK/HjH/8YGzdulOx7fHwcZWVl+Jd/+ZfLC7rXXHNNjuk9sSIqvciHpKUgGQWxWExODQAydVZ1TGKnkEbPpAwxSJMyxv/SwpA4m81mK7B58/l8MBgMmJmZweTkpDgDvbX/LaxavQpWqxV33nkndu3aJQ8mHA6LGfbAwACcTieWLVsGl8uFLVu2CK7MRcYslRtqadC8cuvWi97H93vtf/NNec3vfe97CIVCyGQyQmZnQ7K6ulp8dSmYYPMHuMBF5C+On0kkEkLIt1gsKCkpwbZt2/Dmm2/C6/UKH3T58uWYn5+H3W5Hf38/bDYbstms3PfOzk4xqGcmNDIyAiDP66QFaCaTkTHmbI7lcjkcO3oMbWvacObMaVx11dUFwTIajYoIwmq1orS0VGSqlK56vV5otVqcPn0aBoOhwISHxH1So3h4DA8Po6GhAclkUg6syclJfO9730V3d48wEHg4MwAyS2aDlOu0p6cHc3Nz2LZtG0pLS8XzQO1k06f59ddfx8GDB3H27FmUlpaivb1dAgmNd7g/GKBzuRycTmcBjBYOhyU4j4+Pw+v14siRwwCALVuulLlwzPyZPauzDJntk645MjIiqi9m/KrpC5MtHo6kyfEesb9DkQI9EsjLJxygQnLEoel/0tjYKA1bluxM9FgpqTAd77der0dDQ4OM+VLXAIP65OSkJH6k0Y2NjaGsrAxr164VVgmbjnzGZEOFw2H4fD5EIhHps7AaZCLJ+0KnwVQqhYGBgcsLutdff32O2Cczy+LiYoyOjspoEU4yJceW47iJy5Jjx24fAxd5tQxuDLg8ZcbHx4XaQh6h1WoVPiu13hqNRka/ExcjqR3I49GqDHRhIe9MRcUPAwqljMRlSGtjsF2K3XJjqaehGpDVzHWpUoUlrsrdVS+dTod/+Id/QC6XH8RptVolg2Rpw0YVhSfhcFiCrerFSslmJpP34CWuSlOVjRs3SrMsFothcnISr+3de9E18X6vtWvWyIRdt9sthjIDAwN458Q72LptK2w2G4qLizE8PCwQ1sJCfqqw1WpFeXm5UMko+y0qKsLw8DD0ej1uv/12gVLolJZOp9Hc3IyXXnpJKikOtqyrq0NxcTE6OzuRy+WE18nqIhaLyeBKbl6KB4xGIzweD+68805MTEzA4/HA4/FIEGZlR0GATpc3M//e976Hjo4O/PSnP8XKlSvFjY4JCt+LSUsqlZLEhqwMBr9Tp04CADo6tiCTyQgFj9ko1y8/C1WBDA5supaXlxeY2zDgqlJ9BqqpqSkJWDwc+Xlo98msmPi6av5NcQ0AgWsIKwAQB8LR0VGBCBlvGPzU/afV5qXYOp1OeNDEU7VarUCOAOTgDIVCMkGD1Dij0YimpiYRSrlcLlRVVeHgwYO46qqrJJbxe6TTaeHoTk5OIhQKiQUAE0IeQDMzMzh9+vTlBd3bb789R6xscTHvcsTsoa6uroDRwHKMphLUwVNux0VFehfLAOJTpPnwRGOQUjvybJQxcKvl+9IMVC0V1EulwfDi51czWhrrsIm3NHCq76vya9XPtri4KNm6CrdwofAXsxG+ViqVwjPPPAOXyyVZDjfF3NwcbDabdKKrq6sxNzeHw4cPS6nNYEv6FasSjleZn5/HFVdcAZvNht7eXrS2tgopPxaLYcOGDTh37pyU9nV1dRgeHobX60Vvby9MJhM+/OEPF9BmeDhyfbDhx9Hg/f39GBsbkynRJpNJyPuUXzudTuEk81AOh8OYn5+H2+0uEISEw2HUnx9sSc0/10A2m5WxKWvXrsVPf/azi67x93sdPnQIBw8eRElJCTZt2lTQLOUaI67JrGvjho14/jfP45e//CW+8pWvSJO0qKhImnYs+zlwMpfL4Z133sHrr+cPvvb29QAu0MwIgdDQntDL+Y65rD0GN64HNrHZ8M7lcrDb7Zienha2AzFZtRKdmJgQiiazdwZH4tQc98Muv8PhgN/vR0VFhRzoXBN0AmPPJ5vNz83jQAPO2OO+V/cXfbS1Wq14NQMosGbkZyftbHh4WHoGuVwOW7ZsQVdXF6qqquD1ehEKheD1etHX1we/34+dO3ciGAyiqChvF2Cz2eTgYwAuLy+XyqiyslLoq+QRZzIZ3HHHHZcXdD/72c/miGOUlJQIPlZWVgabzSby1aXNJb4mF0o2WzhuhRgfsUEGJG4aBic18+Xfq59X/T0Dpvr/F/3SSzJSFTrgezC4MwgSJ+NG479VPwPLLS4+No74M6oBDBss/B4qvJLL5dDV1YVXXnlFFjkzkFQqJY0S4m+5XA7Hjx8XniEpTFQ6MQtSDzn6Hm/fvh25XA6vvvoqbDabcEqJ+w0MDGDjxo0idJiYmMCWLVtQVlaG9vZ2uUfq91KpPrxPLBUZNGZmZpBKpYTJ4Pf7xU2qtLRUJsDOzMygtrZW+I/BYFD8PL71rW/J+stms3j66acxNTUlJW4gEJAye3JyEu3t7UilUuju7sa5cz247bYPCZODB9v09DSi0WiBCKO2thb3338/qqur5Xsx6+M65tq2Wq34X//rf6GkpAS//MUvsXbdWmnoEhpixsQD649/zDeSnn76W0gkEnjhhRfkICMGywpSo9H8RSqRlStWCG+Z8mDaX1IEQ4YCezJszHEGHL8Dq1W6drFxxwqEKlUeOCr2S1aQ2WwW75WlyRb3HfFgJkibNm2SyoSVCg/9dDoteDqb8yQB0CuYWTXxWB4AIjbQowAAIABJREFUfr8fY2NjaGpqQkNDA4xGo8BcGk3eRF/9TmResLpZWFjAV77ylcsLun/84x9z/BL8orzR/CIMiirxW71h6p8xMKsdZL6mGoyXNp548SGxQaEGyaVBVw386rU0y1WpWktfA4CUeAwqpAyppZaa7fF1uGDV760eGipbgZ+X/54ihh/+8IfCD1XLTZqNkDJDWtbg4KBkm8xCnE6nCC9I04pEIli1apWoj5qbm5FIJDA+Pi6z2JYtW4bi4mKk02kMDAxI44AGKEajETfffLMEQFW6rVYEVHuxmlBt+NiEVOXY6s+y7E+n0wgGgwgGg/D7/YhEIlhcXMTnPvc5yULKyvJj2Z9//nmZPLB582acOHGigHqUyWTQ39+HW2/9IHK5nJjRpNNpsTFcvny5PH/ya0mT0mq1f1b68plTnPDBD96KXbv+JHJffretW6/Evn1vFvC6uR+YRao0QzYEeS8JD5AW5/P5ZChkf3+/cHP53OhtMTg4iOz/3d6bB9l5VWe/zzmnR/U8t7pbUsuSJdlGxpZB+MY2Adt4CGCbwbGhbkioyleppIDKPyQVUyTcFJWkUkWRVCAMIUzXBgN2+PDFWAQJ27HRZ1sIj7I8aO5udbd6nrtPn+H+0fxWP+d123yley/f/aPfKpWk7nPed797r/2stZ417Hw+MiW2bdsWgcfFxUX19PQEj9vQ0FCSaZTPrzTMQeFDAU1OTkYmAi775ORk5LC7xU36GRQAMuD7HIDs7e2NisT5+Xm9/PLLam5ujvuOjIyEhU/FWSaT0Ze/8pXXQbFfY1BR+tCHPxQdyiYmJvSWt7xFS0tLevHFF7VlyxbNzMxoaGgo6LyRkREVCgX19vaqra1Nb3vb20pyteHkSX8jjiFJN9988/mVAQMqbCYEBSHzdK4kkDDBgJKnS0mKiKe06kI46Llb4eNhIXmegzPC7CDqYO6L7uWfuA1Ea11xOF1QVlYWx0CTJoMlyZiJOMM3ew6tR7bZpPwboMLNq6ysVHd3d+QJevcwOiPNzc2FJUx+JUUOWLqUz5aXl6u1tTUIf/Ig3/nOd6q1tVVPPfVUCCPKDYXa3d0dY/J2engpBJAcYDyohfXu4Azw4gGxjuTTws03NDSoubk5ev8yT5z7Bhc/OTmpV199Va2trZJWijI4aeNNb3qTnn/+eRUKKyXm7373e1ReXqH+/n69/PLLkhSudrFYjIwRODrm37t2MX48FLwbZOHmm2/SN7/5rZIih29+81UdO7aajQGw5nK5kiITFBsK1I/DKisrU21tbcyJ7z28QXpi4KXOz8+HpXz69Om4B1wt7+CZCexV9i73dIWJDHvKGQVDfA8LEMqRtWcfU8nGPHNvKDFKopeWlqLwhJQ2TubI5XL66B/9Uey/gYGB+N78/Lympqa0afOshrYPRRD53LlzOnTokNrb29XV1aVjx45FBgq9LNra2pRKrZS0b9myJdYE4whunngL7/2brje0dPft21eUVt19hN4BhAUCQBiMW3RuPeJ6QMbTvyHpUkirqVeAIJ/z4BSfcb7XK0PIp+TeCIGPk+9ADTA+53h5D0AbAYdI5/RSANyBJAm2yTlPWsEsYGNjo77+9a9HPuHs7GzkXRKgnJ2dVXNzsxoaGnT69Gn19/crl8vFCQooNy8/RiFkMhm99a1vjfcDFCmqyOfzamhoiPr6bDYbGQD5fF533HFHSYCP6C1zDq3B+8E1IzeTk5ORnoTA4k0w1wAzIMhGZa3d8+EIm7vuuksTExPasWNHAAYduPL5fFglY2NjEXih0fXHPvaxcDsJ8iwsLERwjBxjvLw777xDkvTtb/+fwa2yR5Aj+g0QjGbjZrNZjYyMhBchKQKCnH5NVkRlZaU2bdr0Go+CfwN8BHWWl5cDBOE+kX86ZMFzMm7iMp5fzH5AsXhswmkx6bUBYj7rtGJS1pkr3x/8Oxmzca+QPctYfBz83g1GjIhnnnlGTz/9tE6ePBmHAMzNzUWGDUZHbW2turq6dMMNN+jSSy8tyR5KevNJr7tQKLyhpfuGoPuf//mfRZ8414ZJYOLhXAAkk1osFqNyCJKbyXD6Isl1shDci/H455PWMODnXamcAuGeLBr3TibOl0xUgi92yxy3cmFhQc3NzSXKIclt+rvwh03ioI+QZjIZ/cu//EtQDCz6r+u7w0qiiOHcuXPq6OhQX19f5HUSVS8vL9epU6fU0dERHb96e3vjyJ1t27aprKxM/f39uuSSS1QsFqP6sL+/PzYpAY8777yzJODBmjGnPlfuSqLgkBm4W2RiLbnyucONa2lpCdd1YmJCTz31lO67774AfzJeyB2nfy95xWVlZTp69Kje8Y53qL6+Xtdff73Ky8vV1dUVXCNZMtlsVhddtEuStH//AeXz+WiycvvtH9Tdd98ThSvIGkFm3oFTGsgEwPOiShPOFy+ksbExeiyjbBxQuC/r5JkOXV1dOnXqVEl2BQDr1i1r5DEU7uNyy95271daLRbiu0lAdqrJ19VpN99LfAfZd6PHjT7u796Pg6zfl7F73EFSSTCUsUAfgkvuxRKUxntzo8yxqlgs6h3veMf50QtEAplcH8xaPKWDc9Iy9smHP2IifPH84jPOma2lNZOfZzF8oV0zORD4/9HyHgldayx+eUAQ0J6cnIx3Igjg93FQwuLA0iHdjaDO1NSUPvzhD+sHP/hBuJiMk9JqEsF5b9xjLBwixUtLS2pqalKxuFrNR/ofx8TgxlOMQC9f+puSroXLSNEBc8Mf5CWpdJgDlMv8/HxwaKRP4bZzJQOPTm8QSe/u7taVV16pwcFBPfroo3Hg4+zsrLZv3659+x7Sli29UalFGuP27dsjJ7i2tlZ1dXW6+uqVnOuHHtoXJatLS0v67nef+XW+7Iol9JGP/IH++3//ke6993taXl5J76P/AT0leB5BZ4I19BiZnp6O9e7u7o6CADdGAGR4zkKhENk/rC+c6OTkpFKpVOTlAlwOYigU1grvkyu5X91qd2uStcAI4uduobJHHHBRDC4vvA+GmcsNRoPvbb8/Xi6ylbS0+XkSgB28oTbwCN37IpZC50OXb57rBRWOSWtdb2jp/td//VfRtQIaLxnIco61omK1+TdcXzIFbC3gYiKcqnCXw61DPpt8vlvPCANWtQM0wpxUDgABl4/TU8/cjWC8yWCaB9X8PZPvQvUW94U7hN8jkrpv3744rTdpvS8uLkYaDpv6+PHjsW7edJ1jvjlhOJ1eOcRzz549URjR1tampqYmHTt2TIODg3GsOKexcrzJtddeGwn8TiexNr52bukSKMtkMkFVoHQYo7Raeo3MucLx4+BTqVSciHvNNdforrvuUl9fnxoaGnTTTTfpq1/9qqamplQoFKLslV7BQ0ODevjhR0oswOXl5ej+RrARSzKfXzmh+JZb3qtvfvNbAYQNDQ0l1AgFGh4IIw9XUlit9AxgQ7uFBf2FkiLLgr0lKRpMobSSRg/l9KzFWp4JsuR7y/dX8iKYlgRq35esOUFovE8+60CO3Cc9XN9ryb3ln0Pu2WNrYYMbiL7nk3vV96djlY+JYGaxWIzDMbm453XXXXf+li4v5NYGws7lgSqSsicmJqJAAa22ljZg8n3S2JC+2ZKL4JPkL+xaNJVKldSscy/Pq/TLQdm5IKc/3I0B0Hzx2RQIGOOBGpBWwIQGGwRJmpubo8yXzT0zM6O+vj7V1tbqggsu0NGjR+OdyVmlKsyDVwSg6CtcVVUVjYe2bt2qrq4u7d+/XxdffLE2btyoqqoqnTx5MvIrd+7cqWPHjmlxcVHbt2+PKDUNY+BhOSIFTtCDJ66I0+l0UCjFYjHyU+FXR0ZGwgpMpVIBSCTu8x231PgcazI8PKzKykrdeuut0Y2ORjKDg4O/bnh0Uun0prBaqqurtXfv2/SVr1ygXbt26cILL4zN39bWFh2oSJsi+2Fqakrf+973lcuVB79MoRDgm81mg1MHjGlcRP436X4YK8Q3mOOpqSk1NjaWBHrJxMAa4/kzMzORx1tVVRVNW9gDyCd7ib8BMLdWMVqS4OT7PenBsQewEJH9ZGCNdXeqkraaa4Fucn+64nDDh32HvDBu9mjyu27xuqGAF+WpqskxgXf00iA91Auq3uh6Q9BNgpuDXNKt52X5Dq0KcbvRvgya7yUtWGl10XleMkOCe6zFb3HPZFDM/369yxWHa19AMGlVczw2G4UAHULgc5bP59Xd3R1urbvx2Ww2LCu6eFHVI61UwHz/+9/Xjh07lE6nozSVSiYHeAAXIKD8d2JiInjfTCajyy+/XGNjYzp9+rTKy1f61npVG/M1OzsbVqLPD5Yhrh99UZlzsh2SvJjztu4CkuzvfCDWBOAsKRRLsViMLILR0VG9+OKLOnHiRDRjefLJJ3TjjTfppz/dp9///Ts0Pj6uiy76Pd100yfV0dHxGjl3T4az98rKyoKnhUcG8FB6rC1uP/w08ietAFpnZ2fMBwEr/k6nV3v3Mk8E3piDiooKtba2KpVKleSAczTP8vKyamtrI8BEoYwrPzIskFn2Cv9nn7EnHXiT+wYDxoPHHmD27A7cd2IsUIkAG9WIfl9+7593hQEgOlCCHb6uzJ//3D1bxsZzMa4Yz9LSUtChUDQeHyLIyV6RFMcFvd71P0UveLaCA6fzKg6gbCBq8HkRd0H5PELpF6DpVqWT6kyoA4GDKpsSK8y5I9xBt2STkU9+7tSBB3Pc4vV/O9/Ee8/NzWnz5s3R96Czs1PT09Pq6+vTzp074/QEAmN/93d/p7KyskhfYjPX19drYWEhTl2or6+PUzgmJibiTC7cWvJfJenkyZMRkaeX7/j4uPbs2aNcLhcHXGIJ79q1S8eOHYs5ocCAJiwdHR2anZ3Vnj17gnYgr1NacaPHxsYiqdwT3hnj3NycWltbS3oEYDWgSDxDgYAh9Eg+n9f73/8+SdKTTz5VwnFS0Xbq1ClVVlZqfHw8mpTAeWYymejA5q419IBv9nQ6rTvvvEP33POd4GzxXoh+I59Qahz2OD8/HxkP9MjACAHEsdAcBPm3B4yRKQ6XhAqhL7N7A+6VOmAlDRqnAwIUzA33/yd/7/vYs4f83s71Etj1ALePAVxh/hkDQAf4+1wlscPHmXw3Pu/KMPm+vqcJXiYpiiTmOe2KMXbttdeeX/bCo48+WnTr1nlEBup8CG4ULo9ber7wSW6T73puqC8wmp8XhlPiu2gzwIYzpEh9cssNAUf41iq2WItXllTyTL+S3C3/r6ioiFJeNj4UAueTzc/Pq76+XmfPno2Uor/927+NPrYEvFpbW6MMuFAoRK9S1m/btm3K5/OhaGjCUSgUomcs8zo1NaWtW7dqampKXV1d2rBhgwYHB5VKrZw20NnZGT1i6+rq1NbWFocbFovFAJ4tW7aou7s7AIx+v0tLS3F2HOtAfjPlo7Ozs1pcXNTExESJQvUCCjbn4uKibrnlvZKkBx/8SYyjtbVVDQ0N0Rh9aGgoUtcqKyujQop1A8xRTFizVPmRYkX+tqSIVLe2tmpqakqDg4PRgIj0OhoudXV1KZVKhVJDHgkYOn3gvG0yDoBlhgWMR5TPr3SV45geDu5MUl9JQHXgdjB2UHaONgmC/Cx54d2iENZ6Pu8ENcOpJDzX+X2e55Yuc+QA5++KAoZCSe5NnuVWsT8Dz8lpzGSGQ9IT92e4UsObX15efkNO9w1B9/HHHy8iHGiIZMmvczXUd8M78ZIITJKSSJLiTCqC5qY+kXiei0WRvCdChSWCG+DuCgKDsEilFXFefeffdc2fVCQOGiSS+z0BwYGBAb35zW9WOp3W8ePH1draGo1fhoaGNDs7q0996lNhYeN6Uz3k3ev94D7mkWYghcLK2Wb0LJieno7jtnExM5lMFB/gvnd2dkbJ7PT0dHDBBJLgG/v6+vSP//iPmpycDD6LDIlicaX7GTwm+dhw0Byx5MDAIZxlZWW6+uqr9MgjjwYAuyfCpkDBsJYAPB4OfDCuLutBfXwul4sOaJwkgoKjQ9l73vNufetb31axWCxxGT1ij+XmTbqT8uienrvr0DTeVQugxsshCwG6jrREB7mkdYncubHke8ctOqw+qbS83fflWjTjWlah/559yxgoAUbOnFb0cbiB5PMJJnhciIt9h4L0lES+/3reKr9LAquvM//2y/d30hCVpGuuueb8K9L8SmYh+GCwHLHAvDqJCfMqm+RCEgRIpVJxGF4S1KQV13VqaipoA18IF24HoqQGc47H74/WlF4bwfWE8aSWzudXcjBxXem2hOu8sLCg6elpdXd3a3BwUA888IC2bdumycnJALhjx45FY4729na9+uqrMZ58Ph+9LgiQFIvF2OhYHFiQjLO8vFyjo6NBURDUYX6y2ayGh4clSb29vcpms3EM0IYNG3TmzBn19PSExcExRul0Oo4vIg+YmnM4LrrMUVhx6tQp7d27V0tLS0FT1NTU6IYb3iVJOnTol6EQDhz4uaRMRPMBbADYOztx4jAntWYyGd1++wf19a9/I3rVejN6rNRCoaDa2trInYUOwyN4z3verfvuu1/5fFWsOc/Hcneu061HPCjkB4s96UpLip6z5FFLCo6WPgPj4+NxOsK5c+eiGAc5zefzkT6HkkmmTfJZl2H2nwO4y75/Zy2v1b3ZJP3HnGDEwMu74vH9RpZF0qtmHO6pkv3goO6URSaTiebpvsfBC7fsnTrwKwmygKtjH8aNp0f6+7/e9Yagy40AOAZWKBRCAH3DA4BYO1iaNMEgbxUXFR4uSS9Iq5FWJpOJQeB4HsCXFBQHXX62FrflymMtjojLBY5Fy2QyampqUltbW0mBA++yuLiogYEBLSws6NixY7rrrruUzWZ15IUjK8XgklpaWgMUe3t7df311+vaa6/V0aNH48hrkv9dAF0omCese099khTgw5pgkfL3xMREvAP0BxVQ+XxebW1tOnTokHbu3KnFxUWNj4/r7NmzeuGFF7RhwwZNT09r06ZNOnny5K+bybyszs5OtbS06H3ve58KhYJ27dqlq676HUnSww8/ouXlZdXV1eno0Zd+LU8rglteXh6uOWtLAQTrhGLDcp6ZmQnQ/+hH/0hf//o31NnZqXw+H2l2o6OjMU/MUW1traqqqqLog8BosVjU3Xffo1yuLGiIVCqljo6OSM2ik5i79swvsoHH44ADh859ABD2BA21C4VC0CObN28OS5CMh6TRglJKyrykyO+GmuLzZFLQ1QsFD5hwD36HjDFmxunBZ1cwbgWTEujzRb4wdCFBOQ/S8g7uvrNvAV5XJvwfrhw5Z/4Az+T+cUBnHzNez8V1r4Ex+VwlwXtNLHkjeuHnP/950TUj3fjdTV/L9S55gHFD/iwXWAdoBBOAxf3CLeFZru2Sbj+C7lyLTzJC4jQEE8YC+M/cSqGJc0dHh1paWsJCSaVSUUOez+cjuAVvOz8/r2eeeUbpdFojIyO66KKLtHnzZv3qV7/SwMCAUqmV7kV33HFHtG1sb2/Xt7/97eghUFFRoUcffaRkfjdu7Iq5gOJxq8PfBRdsaWlJ5eXl6u7ujs5hFRUVamxsjMq3K6+8MjYkvCtW9FNPPaVbb71Vt912WwkIVFdXq729TZLU3z+g4eFhjYyMqLW1NYJtPkcUHhSLK/mOfX19qqmp0dLSUrTXQ1lUVFSUHCnDmlBYMjs7q4985A/0ox89EIBHupDnjQOYFJdwqCE/o8NWV1eXurq6lM1mo/0h+c5YSaw7AOQBWixifk4JMWCPPLKpyZSguU99fb0KhUI0TUpevk/4I722eAd5d4vWAdZprCRo+/3c1UauoLZYf86lA+A8iO3c7vz8vCYnJ0soSOaSBvaAHuBZU1MT91mL/vMc4OS7sx88vdHpxiQGOmXgHdP8fp4Z5V49934jeuE3Zi+gWajKgJ91y9Y1m7v7XB4x5P/uPnB/50+41+TkZJxC6kEIaTVVzatRXMPye89ASPJRjIPfAdJJ0JJWNxVWOgCHVQaH2NfXF9Vi1Pw3NzdHWhUdqwqF1fzMTCaju+++W3v27IlG483NzWppadHo6Kg+97nPRZcnwAqBw/NwKoTgQH9/X8mabtu2PWiK+vr6OLCSMuZMJqP6+npdddVVETzL5VZON+AMs49+9KO644471Nu7RZJ05kxfVFbx+Y9//OMhoH/6p39a0m4yk1ktj2X96enQ19enfD4foOvKGKDxXgKctzc3NxfcLvmTNMH34BqWNFVeVI81NzerWCyqrq4uwDmfzwcF4UUGXHhmNLopFoslHgVA7tkNvDtyRQMiD/561o9bl1xrGRrMpV9uJSb3hdMCKAXfn34Pt+j4DmvB+iUVEVar7yfGwjt4VZfnuzuQ4SUg905pECQmcEwQnYu1x4OhMRTv4BlLHrxjj/I7FJIH4/AGnKrwIOh5g+7jjz9e9AlfK9XELVl3v/m5a1BPvk4unlsMfB/tyYmhHhBhYZ1UT/JO3J+iBNf2LpRJsHb3ZXFxUYVCIfpqjo6O6uTJk7EhUqmUXnnlFbW0tKi1tTXArr6+Pk47pSMWXJMfXYRV3N/fr+bmZp06dUq9vb1x71QqpdHRUaVSKb3wwgt67LHHND8/Hx2W2OA0RwHceDePqEIzEHUvK1s5gj6XWzlzbXl5WWfPDpTIwJvffJm6urr00EM/kSSNjY0Hp7ry/zFJUnNzc3CLf/EXf6Hv3PMdXfm/XamdO3dq7969kTZHyStCXV1dra997Wv6+7//exUKBU1MTOiLX/yirr322lgDX3uAHJeVIoQ77vh9PfDA/xVpU3wGXhhXnrPuqPD6b//tj3XgwM9jPjAokvwdIJiUFX5H/1bejw24vLwcMkTgjYyMYrEYneBaW1uj6XiSCuO7rLUbHW6xOcXhe9KpwaQn6vm0a13sT6dl3KDy+wM4yYwBfseYfN6SHK9TfJ5Z4HudsTIXQ0NDQcV5cJHf8z2fG8cq1smNPubc9w/vx7yQ+ui9GPjdDTfccH6g+/DDDxdZGOc53CLkYc6rJl0UXtwXgcu1g4MiFuXy8nIEDZJg726L0xSuzZNcr49/rd/5ovT392v79u2SVo79Pnv2rDKZTARRyBSoqanR8PCw6uvr4x5Yjpwv197eHhFcUpM8WMIx9mQwLC8vq62tTcViMRp6kyzf2tqqw4cPR7/P0dFR3Xbbbbr//vv1i1/8InomAFJ8j6YyvB8CPT4+rn/4h3+IPgukZ9177/c0Njamiy66KPoteLELwrlr12r/WUm67rrrdWD/AUnSBz74Ad1//33xuy996ctqbW3VM888o5GREY2Pj+ujH/1onASwuLioF198UcePH9db3/rW4Cop/XWvgyyE66+/Tvfdd39J7iyuuqSgM8iqGRoa0o4dOyIpHw7eYwfIR5Ie4/lkcvA8TjyoqKgosb6gfCgnpo8DATsODl2LH/Y94qDvVN1adJiDYhIM3HNzVz25J7l4Lu/uY/B8WfawK4a1vE0HWJQC2AI4ch8PuCcNMsZIkB4jxRUlwJnc714D4GDs/cHdi5dWe8M4JcH3GIMbOlddddX/M9B1wUsupoOvT6T/zBcegPbLTXkWC9eCQ+jI8/Tgmk+Quy9OcWC1enmiv7MvEH9PTk5q3759mpqakrSSMI/r2tjYqKeeeko7duzQBRdcoM2bN0faFXmI8FDeCcvfHReTZjGpVCoS9bHumYuhoSE9++yzampqUktLS/BSdXV1amhoUG1trZ577rlwSaWViPi9996r9vZ2dXZ2RjpSoVDQ0aNHVV5erh/+8D8kSTfeeFPk1P7lX/5lnHwLQFRXV2tgYEDHjx/XBRdcoK6uruBRv//976uqqkpHjhzR+Pi4Wlpa4qRmXPqtW7eqp6dHudzKoYBsXIJjCwsL0WCG62c/26+hoSH9wR/87/Gz73znuyU51WQ1vPvdv6f77/+P4BJzuVwAnMcgyJEtLy/X3r1v1cGD/yPmPmmJOeVE8Alemc3twMW7esAGy5rPEdx89dVX1dTUpI6OjrC6GUcy4yBpjTnYMEb3KPmdA7AbKg6EPMPv4+DsAOjgxBiZL5QW+dcAE+/sGMG9ff/5vnVaD/l3r4335IAAB2h/rj8jqcDW8sb9ciPM/zit6sH+5PvgCbz97W8/P9D9+c9/XiwUCjGhHpl1jeUvBBi65uZFk+lYfAd3zC1n7o0LxiF6zvcCtgiCBwacbE9q3OQ7uyJIpVL66le/qhMnTkSAw1vmwUlSEw/AVldXq6amRtPT06qsrFRPT0/QBNXV1XFUjaSSgFBNTY1qamqiZp/ULMp44RMzmYwefPBBbdq0KY5F37x5sx555BGNjY3pwgsvVC63Ujra09MT5ztxHtkll1ysoaFhFQorZar5fD74TLIRfvSjH+nOO+/Uzp07NTg4qA0bNmhycjK8jeXlZQ0MDKizs1MDAwOan5/XRRddFEGipqamOPwTV5k1KisrC+4QbjyVSoXXQFoZlvno6GhUz2FJcizK+953W8n63X//f+gDH3h//P8nP3kosmT8OChJ2rv3rXriiScDuJPWEa7k0tKS5ubmSqLuUDfIngdUCNCRLolFhfxhGZPBk6QD3O12JcAe8Ch90jhh/tzSXAtM3XIjSJp0yfleksbw2MlaGUtJHEhyxz5m/2wSR6CCnJ7g97wfgXXHFTDHaRLHGC68IVc4SZBOYsNanrIrQJ9jfnbeoPvwww8XkxPKZDjiJykCt3jhLl2zuHXKhvCqFCbarVl34ZyzS7pOvtgecPCxEiRxa0Va0drNzc36yEc+Es8pFArRLYr3ouwSEIafwxVmfFh2uMZYzKQqVVVVaePGjWpvb1dbW5uqqqoickugjej1zMxMaFzfVKOjoxoaGgqX9W/+5q8lSffc8x21tbWpt7c3ehT4hidVCrdpdHRUU1NTeuWVV3Ts2DHdfvvtqqmpCYAnlWkm89M2AAAgAElEQVR0dFSXX365JiYmdOzYsXCNZ2dnI+rux6e7BUi117lz59TW1qalpaVowj4zMxMnM5eXl+tXv/pV0BmUjzIHrHt/f7/Ky8ujNeW2bdvCQGAdKioqtGfP5a8r408++VQJoCSBynOCUSJJQEunVw9UBBiII1B5V1dXF70TJAX37GWybjDwDOeHfc95UNhpD3fjfX9ICoVQKBSipwAWrb+LKwIPaDloJr/D7wF/fs7z6DECP+sgxzsB5m7U8UwogampqZJqTB+Tv7MbZkm+Gi/GlS6fSZZJgyGu9LhcqbgHIkm/+7u/e36g+9hjjxUdUAlAuHnt7h6/Y9CeO/pGbg3WSLFYeuSL8y9YaJlMJlw8sh5c65IWxSR5JQv/J/mcsWYymQhCPfXUU7r33nujr6Z36kdRcAaUTzadvnziAXjcaCKjNCbByidCXlNTE5u/urpaDQ0N6unpUV1dnZqamlRfXx/HYheLRd166y2SVvq+VlRUBIeJFYVVDjBj/Xd0dOjqq6+OHgUI0/T0tJaXl3XmzBmdOXNGO3fuVE9PT5SecnAgVXd079qyZYuqq6vV2NgYc+pygSXMxkM5kQ9Mvi0nLxNtprCCZ4+NjUV/h1tvvSXSwzhYkY3LHLDBfQO5AYASYw1Rsm95yxVr7odf/erpoAlQxshrLpeLjm/w8zMzM6qvr9emTZvCSsciI8rugOkBKOaBz5Dni1y5Beb3Yc49/ct5aC7mxuXV96V/D7n19CsHV/dS/XJL0K1ZD275M5PGW3KtePb09LQ6Ojpiz0G5SauUhlun4JR7uvybMbgCdKxiLh33nDICF1CivO95g+6jjz5adG3jE+dWpZvrTJIDLy/px4IwgfBhkkp6mrpWRkCYJDjB2tra11jVUAJMbtJ9SAoA74D1+4lPfCIAYGlpSe3t7SHMpAAR9ZdUUpYrlbaMw8r3oBOpd5WVlTpx4oTa29tVVVWl6enp4Mcg9F9+6WVWSVdddXVwyuXl5cH7SitpUzTg3rVrV6TfdXR0vOZwRDa8W55Yc7wz6wewZDKZoDjIBjhz5oyKxaIuvfTSiGyTa+rpNHDJrBNzwRhJ6YHy4N0qKytVV1enfftWFEpPT48GBgbiKJt8Ph+9fOGsCQRyb7cYPQKP5cLmRlG7q+v8LMaApyZ6Q55sNqvBwUG9/33v1/3/cb+amprU3d0d2RpkNiAnnpLkwJAEGTY24IWxkQQG5M6NFTcInD7xZ/r3nNrzfePym7zc0pQU78Dc+uVWs6QSpeDj8f2cNNLcePO0Le7P+7gi43euAFAieK90fwOzXGmx9u5xvx5PDI9fKBTOv+HN/v37iyyIawUXTv+/uyT+gs4B8TMmVlKJFgEgEWi+50EUJnRmZiY4QdeMrgzYYE7U8wzGRSZBRUWF/uRP/iTei2i5Wwz0Qq2uro4gC0JEY5lUKhWBG2m1Abyk6JNKKhzWLkG7dHq1T8MvD/1SXd1dYWFAD7BpoTpQbuSotrW1hWUJAFF1RovAzs7OALOGhoagM3C1FhYW1NraGhYkVVsIO94BAN7e3h6nL0DHSAoPxQWXtWhrawtqpLGxMQJ0rsReffVVHT16VPn8SmtMmsoA3MgDVAb/93JzV4QoZd+o7sllMpnIL+XdkSfmlOY1WO2Li4vq6+uLwCXWM++MIcGecKWfdIc95xMvyWk2PAN+hgyzzngJScssyVvyzg5QScqA77rxw+dcJpOGjeOCu/msm885HiD7BMMDzEFZovTcgJiYmAhKymkKVzjOsSff0bMQfE74rle78j783q1vPAhf26uvvvr8ei/gCjnnwYA8aMXF57A2ACle3gfKdwEQLA5eEGGura0tIcldK5OihVBgkfmmRagQMJ8kNDiBHazNmZmZCGw5p8T3cSWd/5mZmVF7e7tGRkbCxXQr+ty5c2HZV1VVqaOjo2TRyTklGJfL5dS7tVc1TTUhEAAt2RvMNyAD4EOVUEUFQL366qvxLul0Wg0NDeEiNzU1xUmpjY2Nqq+vVyazcsRMOp0uCVxgoebzeTU1NWlhYUGHDh3SZZddpmeffVb19fXavXt3zBWVWel0OirOqPTCcsdKwBKfmppSa2urrrjiCh0+fFgHDx7UH/7hH6qnp0epVEqDg4NKp9NRyOAb8i1vuUJPPXWoBOiTVqIDEf/mHqwra8xnWCM8M8qD+/v7tWnTpsjsIH9aUihzd7+LxWL0BnALyzczx5AnrSwHpJKN/Osxe8m+7wEMlyT/6HPie4X9zthQQG4UAfhU/wE8bn26tejWs+dpJ0t/2aMYIJ7Sh9GWSq10xBsfH1ddXV2Mi98xdhQeoA9+sFecomRu2Zd8l3kDa3hHB2uMsFRq9Zy117v+p1o7+iK6a8bEA2De6Ibv+YQ5sc33OBOJl/JeCyyca6Mk0CP8uHG1tbUxmUywu1mMy90UFuiXv/ylvvOd72hubk6p1Ep9P1Fwd33T6XT0fyAXN5PJBCeLInFXB5DhhAiPZgNOgEM2m9XMzExwVljlZWVl4QpR108bTYJN6XQ66BFSp8rKysIK9yAHf9NNDOGh5BZFgyWVTqe1ceNG1dbWBjDznKmpKZ0+fVrV1dXq7+/XiRMn9Od//ufBx6bTK6lVdCTDpa+oWDknzCP9GzdujCquubk5vfLKKzp9+rQ+8YmP62/+5jPKZrPasWOHLr74YhUKhTht11s3Dg8Pq7e3V7lcLqgj3gOqhE3kz+bcN9+czAH3z+fz6u/vV1lZmVpaWqLZDgFKZJs1xnBhDgEGrFnWnz2FXJICV1tbG1QVmxxwxdvylpTJSL6DOfcAJAER9inyyvqwB51zdYs36fFiPEmrdCFg6Jw1+50cZkDQKQMHwSSoM2a8qKampphHxx5AmjV0ECWTxnEhGajkmc4BO83BHuI+mzZtUktLizo7O88/e8G1FZ91IPPJRGgAEI8gIzQsDnmVTAREt0fn3SqVVgMsTnnwGf4QZZ+dnVVtbW2UeI6MjISwUFZMUAgL5mMf+1hY38ViMRqOs0ndajp37lykM5HP6ilyaEkUTCaTiWixu71lZWUlrRYRPk56GBsbi43Jxsrn8yUgmeT/ysrKouoMgQFQAGY2Fus6NjYWXZnq6+s1OTkZucOkUNE1jf4NFRUV0XvBTzNIp9Pq7e1VoVDQBRdcEJRIdXV1nJacyWSCKpCkp59+OsCpra0t3gO65Ior9ujll1/R5ORkNFjfuHFjKPq3v/0aHTz4PwKQXK6QUarx+Mzs7KwqKip07ty5EjpoeXnl+HKUZCaT0fj4uFKplRQ3sjn8dIa1rKHFxcXIesACIiMDBcSa+4YGcN1S9Yt34fdQIh5HcRrNXW0vF2eOHDzYt8w948aD9LEkeVsu5hGahnfAQEBOnbLAWPFScqdgklw1AOkFW5R3u1XOHPFObsAgY3iNSePS6Uw3GtlHS0tLGh0dVW9vb6wx1YfnXQb82GOPFX0QvIQjv1eEYIWiwd1Vcb7FNbDzNfzcK0YQmuTEA9LV1dXBLVZUVGh4eDhOVfXFxaKan58vaW6eyaz0Tqivr9d73vOesFZxobds2VIiQCzW1NRUlLZyYGPShcT6nJ6eVjabjWg9ggRlAGAB0u6KZTIZTU1NBfCSekaTl0wmo+bm5hBSQJmeDmwe78fA59wK8+AhgJRKrebXsjFQbjQgYjNSCMLhiQsLC+rs7FRb20oDnFwuFxFeLDPWn+PgKysr47SJpaUlXXzxxdGcvLm5WXV1daqrqwvKCQXrXKjz+978xykZ5JU0L5crSWGZj42NqaurS1NTUxofH9emTZtCpjkdJbk/2OCAAUrLPUP2iQeeHGCRI+7vzVzy+XzMj1MDHvfwIDLfccvdA0vsSbcg+du5XO8e5u/BmF3uAVY8Ou6JrHgmhO9TxoXCcUXmVi/v4/Ls+OBcPV4msQLegSwYt4RZKwddLqd26LT34x//OKooR0ZGVFNTo6uvvlo9PT268847z7/3grsOLhAOiD5oF15/eRYWwMStY5Jxo5gkB1dfEL4D3zY9Pa2WlhZVVFRE1JuDMcnxROAAQXdP6uvrNTY2pm984xt65ZVXovsRx63APybLDKkyI83J001Y2MnJybCgnA/1nEXSyXK5XPBCksLahtebnJyMefbgTKFQiDPRcHHhOlEgjAkukg0Dhw3virUOmCKYKBzWCWH3z01MTKinpyc2Fbzxtm3bYmzpdFrDw8NqbGyMgxwpHiFdDati9+7dWlxc1MaNG2N+0umVfGVKrr/1rW/q4YcfCVnjmQ5ikmK8vBuby2WK4hWq8RYWFjQ6OhpVdRTneJoVmSvuArPOyIIDczKNyyk51pzxOIh44M8VBz9z7hHwJa7gVN3CwkKk3nV1dUlSFHQ88cQTuvTSS6NJ+unTp3XZZZdpbGxM5eXl0WPZ+XPHAwdd/mbsrAXKAyXIfPB+Se/ZrVSoHcbCvmAszHE+v5L9QyUi30c+PKXMKT2UmKeoplKp2I+OWZlMRp/61KdiTjA60ul0NLa65557zi+Q5gN005uXAxjdrfHMBdfWnueL9QVdwP/hqCDPuQ8bm88QxCFaj8AuLy9Hf1cWkQUFaCDraWb9/PPP68UXXwwghQfN5/NRDYYV6gLj1WIeDEQoqWYiJa6pqUn9/f3R6cqvdDr9mt7CDggEcZzTI494fHxckgIssJyxorDk4GcZP2sDbeKZDngq0Bf0XWWTERjjJANJamlpCXfd3Xu4wZaWlujqhZfinDHykM+v9Eg4e/aslpaW9JWvfFmXX75HNTU16u3tjQKTkZER7dv3Uy0vr+biemzA58+pHCxF5BNFxPcOHz6s8vJyNTY2qq2tLTwL5BTQcwsJwyNpgMDvurL27xQKheit63yvnyvnytyDZwAzm519iYx7rjr7E/6d5jDMfbFY1O7du6MhfHl5uTZv3hz3o4n69u3bA6zc9fcAGXOZtJ6hRDytCs/EsQXZA0uSAXxoSs+zx5NA5vHC3ODysSDHfjkl4Rdgn7wvvVWgLFjTkZERjY6OvhGs/uaTIzwdAkFhcEmBcwuH76Ct/XtMNByifzY5KQjpwsKCxsfHIyUJ6+W73/1ubIju7m7V1tbq9ttvj8YmWFDQCZWVlTp+/Lh+9rOfaWxsTLOzs5qdnY3qJqyN5eXlOF8LzcqcwPPScIX3ddc+lUqF5c5Yy8vLNTCw0mfWMxHgCQFfLDtAHBoBd82tICrLUGAEAVOpVJxkC8eLgCDQgCMC6xsYSwFg8rPmcrmcxsbGIsgkrTZKn5qaCiUrSWfOnNH27dtjzBs3btTy8rJmZmYCAOE6+T/r8MADP9Lll+/R7t27NTs7q5GREf30p/t0882/F7nOKFY3BlxpSwoaSlqhDubm5lRfXx98enNzswYHB1UsFvXmN785eFg2GMoLAHTuF8+J+WMcGAjMLZ+XVpunLC8vh9vLxT5ifzll4lQBGx5QYU85v896Ja1RBwpcZrrhsQ65XE7Dw8NKp1eyXFAgxeJKlg956uw93wPIFQBN2a7n5BO/yefzrzlIlbFnMqvFRMwXcQNoBjCJuAXjcVoE48jpAnK7WU/WzYP+zFfSy33ooYdirjnINJfLqbW1NcbxRtcbgq5HQ1lAFte1hoMlrrO02tPAtRoWmgdzXIjZSAjD8vJyHJDo7feWlpZ09913BwgtLy/r+PHjKhaLOnjwoLZv36477rhDF1xwgYaGhiJF5/nnn9fJkyf16KOPasOGDRoZGdG2bdsCrKApOGUXAZ2amlJNTU30xMUdxcWkg5fTJgRaAFgCVJwK61Yli0wUHkGmB4Bbqh6EJNtgeHhYxWIxKAV3syihRcAZD0rChZE1ZNNUVFSopqYmBHR2djb67LrgoRhwabGgc7mc+vr6dOGFF0ZWBsUNi4uL4RnAtedyOc3MzOjAgf3au/dt6unZqhMnTqiurk4//ek+vetdNyifX831RvbcamY+pdViFSxyQB0grKqq0r59+3TZZZfFkUqSwpti8/v9WDcUMT/3seDiS6Wcq+8d1tw9RPYBz3VqBMsVUHJe1UHWgcVpOvYlCsVbjAJ6Dtoe3MITq6qqCvlgjpAr5NJjNZlMJg5cxaLHMuV9GBd0m/PvpJSlUivHeFHIk4zvJC1O3tNTw/BKeZZTjb6OKD0HcTeCnn322eiPAv40NDREtpEr/LWu32jpeoSSxQFUEAq6OQGYpLMwaHc9XjMA0yy8BIGYqakpNTc3q62trSQVRFrRPoODg2ppaSlJxC8rK4ujtv/pn/5J27Zt04c+9CG1tbXphRde0HPPPacTJ07oTW96k+bm5nTttddq//79UYbLRgLoABM6Z1HYkEqlIngGXYBFgcXPhoASwVoloCApgMc3IelgWBNscpQRngACWFtbq9bWVi0uLoYLT8ALC8+1tXNnzhMz355Wxu95Flw5m66qqiqOY0IoiUBT6ltXV6cjR45Ed63p6elQcNA0PodLS0v64Adv18mTeZ04cSI8rve+9xYtLa2m9jA+rMO1ZMotKremmIOlpSXt2rVLzc3NUVrqG9O5P7wRvAMPhno+qFNvzvE6RcXz3YVmj0ANeOoVn/VgrXuBeFduNTNHSUsaQ8cVCIDh3oPTE4wPMCwrKwvvByNqdHQ0qAuAy/OGPfjK3DvlByBj5HkFKzw7ysOzcNgLvJ9zzcwjB6wyJ75GDrDc0z1yFDYG0cjISIzRqVH3Gt/oekPQdbfNuTdPWSHY4y/gTV+k1UBa0qriglRHCKanp1UsFqOdIxPLi/OcxsbGiKxjZWCRIVj9/f36q7/6K11yySW65ZZb9OlPf1oHDx6Muv7Z2Vlt2LBBn/zkJyOAMD8/ryeeeEIzMzORGTE3NxeBpYaGBk1NTamsrCyCC54e51QJY8K6pLIraUl6gAYrEAFra2sLgZmfnw/FRH8GX/SFhYXg5QC0+vp6VVRURMaCzyfrAldLcxbepbx85aQFKAzmmtQn3GM2rKQA3uXlZU1PT2t6elpbt27V5OSkjh8/Ho19sCCwDNksDz74Y91ww40lFlhVVZXOnTsXwUaOs3FAAGQBL94Vq62qqioCkqTINTU1hdLwEwZYH9xqz15BXh2c3NoG2Fhzd/sZF/f3wDTjBpABLE+55Jlu5Sa7lmEIQUewZsgXz/AMFldMfMblxC1Cj+lwwRnjsbh3zPw4d++gyrMYK/PCWNzzYl7AJ0/X9Hk4c+ZMWOjp9MoJNJxX2NDQEMHnhoYGLSwsRCERFjBUiAd3M5nVtE3fw56KmAzirnX9xuN6Zmdno9SWiUheaEuPInokF9qAK0m646bQuGRhYSHcTu7NxubFaADy6U9/OjYnQTDq/10T8sxcLqcrr7xSt9xyi9LptM6ePauZmZmgArAysWABtOPHj+vIkSMaGBjQ6OhogHVPT49efvnlsProkevghuAsLCyor69Ps7OzQQuwsdDWKJ9isajNmzdHsCWfz0dPXcBmeHg4An3MM+9SXl4ebSMRUAQS/mlmZibA3S35lpYWNTc3x8YkjxgeDL6aTATn9rFy+IOl2d3dHV224AZxXSmvXVhY0IMP/ljXXXd9jPvUqVMllhHPqaio0Oc+97lYY6+VZ62RHUByeHhYo6Ojuuyyy0oyADwwBgh5MMgj9cg/75qk0DBIHOAciLkHY/JiABQvnpJzwu45Mka/L+/MXvGSaAwAAAGPBCDxvQpoO4CsRQm4kQVt4lhCbq/TAG6QOUZweUDNOV3GA164UkinV1L7xsfHtby8rK1btyqdTkdZO8F21hpajTnx0v2+vj61tLRo06ZNMbfuFRaLRX35y1/Wiy++GDn+6XQ6Uhh97h9//PHz76eLq49ls9bn0eYeIcaMh8PzRXV+C7B0k59/OweGUGPJHj58WE8//bQuvPBCDQ4OamJiQmNjYwH8ACDWGgvG3+XlKwczfuADH1BnZ6f6+/vV398fLhDCzwZ3tyWfz4c1/m//9m+hQdPpdCT/O+eGN5DJZPTSSy/FxmHOEAK8g5qaGmUymTjDDE3c1NQUQa0zZ84olUppeHhYVVVVcXQIPC0NZDo6OsLdpwgAAScCi7XswTk2e3KDV1dXR3R227ZtcVwPVgrrlsutHB3OGtbV1amrqyvWvqKiIk79zeVyqq+vj5QmXFxO+J2dnS3JHsD6+vznPx/jzefzkTvLuOvq6mIexsfHtX379gi6uOwCWtlsNhQBChdwkhRAxvfxHrhPkr7xrAq3+FwO2RP83sePceFjdQ/F+VBv1cj9vAiAAKNzytKqYgB8UQyZTCaqAn1sbgw51eiWKsodLxCLl6wa39/ME/LFfDHXybPp3Ms7fPiwuru7o4iHTAOnG2gGRa67NzICdEn7QhZeffVVbdu2LXANT2N5eVl//dd/HZ4PsR3kmb2aTqf1xBNPnB/oPvLII0VfZHhC0lqcX3Th8aYgLBSanQHiAiddI4+eukZkMpn4DRs2aHZ2Vi0tLXF45fz8vH74wx/qySefDDqgvb1djY2NcR+nPRCyjo4O/dmf/ZmqqqriBFvX+m5pADw1NTX6yU9+ogMHDmhgYCAa1tAdy/mxpaUlNTU1aW5uTufOnYtgorQaZPGuVbx/d3d3BCc43JLgIlTD6dOndfbs2QAlTnrYtWuXxsfHtbi4qJaWFs3MzKizs1Nzc3Nqb29XRUWFxsbGAqhbWlqUyWR0+vRpNTQ0hGXQ1NQUAAoFsby8rO7u7tgo09PTEVzDja+trdXZs2ejFHZhYUEdHR1BleCp+AaTpJ/85EHdeONNIdTwZ9BZntL3z//8z68BsvLyci0sLMShoPPz89q8eXMEA93C9corlCBWD/Lta+KeFkDleZ1Y4cgOGSKAm1tPWHLcD4vcAZyALvEOxuGUnz8TwHJKwXN+k9wxn0lyzQTQvCCC+SJWwfN8Lv2d3bBAwVIh6corCbjOQzMnKFqOscpms0EvYQgyD86Ff/azn1VLS4vKysp08uRJffjDH9aNN96oXC6n0dHR8JygFEhLnZ+fj3RPxkXhw44dO8LzojCCcwZ591QqpUOHDr0u6GY+85nPvC7onjlz5jMuLCwMi5JKpUK4SeNKakB3pSh/RKgB2qSF4InraB9JsXlpnI1A19XVRXBm586deuc736l0Oh3HoFM04K6UX9lsVvv379ezzz6r3bt3a9euXZJWIvVOmQDCF110kb72ta/p2LFjOnfunGZmZoKrXV5eDnc8n18pvhgaGlJ3d3dYhQAK6WBsOoS4srIy6AcUGW4SQAANwf0qKyvV1NSkYnGlfLmsrCy6mJWVlampqSmyL+iLS9ARN4y+CGwsBHBmZkabN29WOp2OVo9OmdD3Ah5XUsl5caxRsVjU+Ph4pBFBgwAqlZWV2r79npjvsbGx8BAYF1cqldK73vWu1wSqoKYo1W1oaCixSLFq3dJiI3sgxQO3Tk0lL9xMj1+4+y2t9lVmLrgvAMMaOW+J7DstkOQMHax4f+acNWR8HlNZK1bjmRROpWDtM1eAdNJ6T9J4fl9JoaxRishXMqjlQI2czM/PR9paTU1NHADgTZJYJ6zj8vJyHT16VJOTk/Hd8fFx/eAHP9DS0pKuuuoqbdiwIbwxMhu4F31F2tra1NraqqNHj+oLX/iChoeHYz6QIQLM7DtJ+uM//uP/4/Vw9TcWRzCZPADgQTNwaCSTOz09HZrMU0jQalgctEgEvD0tw9Nk4BxJffJAHItFapQHrt773vfqmmuuUT6f15e//GWNjY1pZmZG5eXlcboBwsmGnJyc1Be/+EUtLS3p7W9/u97xjnfEmWEjIyPq6OjQkSNH9O///u86ceJEbGgyEhDGQqEQnGixWFRXV1fk+vb29kYTct5hfn4+Mg8ABRYe9wqrD++AeWpsbFRdXZ1OnDihmpoadXZ2hpXEphkYGFB3d7caGxs1MjISBQrMJVYuykNSrBVrjVWRSqWiFSQ0jZcG8xku52MnJibU2toamSI865e/PKTLLrtczc3NYQGPjo6GcnFukM3O/8m6oKABuWNMyAdcHnQEypHxIzcOjFzJII8HSfnDUURJQOM+eHSAEQDlgTFJJRVXUBseawBYUMQANEaIc6vc0zlevu+0TJLb9u8Ui8WggDxt0YuC2ON4W8l35Du1tbVxH+gQFCJzNzY2Fp+jCb/novtZgrW1tdEhkJgH5ylOTEzE+nsge/fu3Xr++ef18ssvK5VK6ZOf/KSKxaJGRkZUW1urTZs2Bfd/6NChyHSanJzUNddco8cff1zbtm3T0NBQxCsqKyv1O7/zO5G2+ZuKI97Q0j19+vRn0JBoFFxb+E4nuEmMhn4YGRkpyTl1Pg03HzfDc9vQnIXCynE1dXV1JSkx6XQ6Al8tLS3RzAbhRDEQ1Lrlllu0efPm0FJTU1NR4OAWD+9SXl6uU6dO6Re/+IXOnj2rHTt2qKOjQ2fPntXBgwd18uRJ1dfXK51OR7VOOp1WZ2dnScQ4n8+XaEOsTECE7AdykDkTDIuTzQunC4hx8gSpegjsxMREWDaACfm5xWIxrA3frFg3WGsAEOvI5gXMsGg5nockfSxmFCWcHpVy4+PjUSKJMkZe6uo+p7a2Nk1NTWl2dlYnT56MrA4UM92/cDPLy8u1Z88eSdIDDzygPXv2xAaA12ZN+eP0QpKHdJB1mfZAkFu7zsM6EHsanmdk+DjcGmQczq2iIPAcPQWO+/t3GKtbrO6uw0/7PZBJr8RCHtzK5kpG5z3A6LLEGBgjlzfk4T5Y48ePH1d3d3d4XF51xhqAN8gvze/5fDq9UsSBcZdKpaKJ/OTkpC655BJVVVWpvb1d5eUrTZQmJyf10EMP6eabbw7Dg4NgR0dHSzz0YrGol156Sddee60mJibU3d2tSy+9NErg9+7dq9HRUbW2tmp4ePgNLd3f2PDGXZ5kAACwQlh4YT9CnEgilmt5eXn0q3WSHasakHdXl00Cf4MidQYAAAYXSURBVIL7mEqldPbsWW3ZsiXcSgSIdoNYNQjA3Nyc/vVf/1UnT54M/pkmKmhyDyohnFdccYVuvfVW1dXV6dChQ7r33nuVzWa1ceNGbdiwQQcOHIjNhGUlrVa+YFUAPl4XT8DHLQ7+FItFDQ8Pa+vWrcEtew8AB4rl5eWSUwoo5Z2YmAiw3LRpU0m6H+9cVrZSoEA+sTcyIkB1xRVXaPfu3XF2Wn9/v5qamnTs2DHlcjldd911OnLkiGZmZtTd3a3Kyko9//zz6u3t1fHjx9Xe3q4tW7bo8OHD2rhxo7LZrHp6eiKb4/Tp07rkkkt05MgRbd26VT/72c9000036eDBg5qcnNSePXs0PT2t8fFxnTlzRnfddZc6OjrU0tISFAHA4iDg1pxz9binzEM2my1ZC/hRt97ZhG61QotJa3fewmUlvuGReZS0UzbsE+9wB5Ahy/zMqwi5B8oMWfL95cE9z1JwBeKpduwzr97icoVBqhgX8+mKC3qIoiMqPv3+KFWnSAByL1Cg4KempiZiSOBUZWWlvv3tb6uhoUHPP/98zP+WLVvijLVcLqctW7bo1KlTqq6uVl1dnT7+8Y/r9OnT+sIXvhAGE9VstbW1OnbsmG677TZ997vfVVdXl8bGxpROr3TUW1xcDBmcmZnRL37xi/PPXkDgnNuUVnkpFwa6ZZHLxqKyiG5lMBEIC+AEJ+lWJxMKVzMyMhKVYQSJaOGIqyJJjY2Nmpyc1Pz8vOrr68OdrKio0LFjx/S9731Pp06dCquuqqoqItdsHhadoF5nZ6c++MEPqrW1Vc8++6w8uyObzaqvr0/Hjx/X4cOHwyvwGu1CoRBCVSgUdPHFF2t0dDR4IvcscNtaW1sDhLEWaE9JUIrNh1WAi0qpKYA6MzMTFmEqlSrJVIBzJcpLAFRSHKLZ1NQU3b0qKir03HPPxb+vvvpqvfTSSxocHNTll1+u/fv3q6KiQr29veru7tbAwIAGBweVz+d14YUXamlpScPDw3rTm96kkZGRKMkdGxsLhT03Nxe9ay+99FLNzs7GfT7/+c+XRJKRRSxu5AzlhjvoLjCAydw5jYa8O7g5T+kUERYlIIa88Dm8BtYPA4U9w/O9jaLzyShK9h5yBai7Feog64E25iOXW+2V65WASTB1azxJl/AuHlz0d3ZuOJvNRlCqo6NDExMT8UzGCzWH9c3veA6WNv93DII6YZy8CxkHTzzxhB577LHI6aWgx4N+ZMt89rOf1ZYtW/SlL31JtbW1euaZZ7S0tBSHEJw9ezaq0aTVfsxQgVjX550yduDAgSLcIQLouX8sWjabjXxNb0rs7hUTwuQ4XcC9vaqNBPuKigpNTEwEhwW40nSCSauvrw+agSos8kQ5ihwLFkEn5eOJJ57Qfffdp0KhoImJCdXW1sZJCklh940I75vJZHTmzJkYpwsr7vbjjz+uvr4+jY6Oxj3a2trU0dGhBx54QN3d3bExPaCYzWbV0dFRYjHMzMxo48aNQffglhGNZcNh9SIYbsGQKkMOJlHyycnJOK2XdYZnnZubi/nu7OxULpfT9PS0MpmVo3ugDDiKiBxfuPienh7NzMyEPOTz+Wjks1b7QoJ0UEnw68zTgQMHXld216/163/lVSwWz9/SdZ7Ko5+4nBD+3okJIEWD4Mp5NJ6Nns1m1dDQEFoMEKDsdHFxUa2trWHB1dXVaWpqKlK9OOIalwO+kOcBgATbCoWVnggcM1NXV6fBwUHV1dXp8ccf1wMPPBBBCzpsuWWI8sB9y2az2rt3r26//XalUisJ1mNjYyX8mlfWVFVVqa+vTxMTE3ruuefCVYafYp6J8vIsFAXuJgEufz+UDaBGm0rStgB7IsKAXWNjY1jHgHJjY2NJUQB/Y9kTRGxoaNDExESswfz8fIwb65x1b29vD8sLBZDNZiNgh1JGITQ3N2txcTHSC5l7/qyD7vr1/9frvEF3/Vq/1q/1a/36f/d6bSOE9Wv9Wr/Wr/Xr/7NrHXTXr/Vr/Vq/fovXOuiuX+vX+rV+/RavddBdv9av9Wv9+i1e66C7fq1f69f69Vu81kF3/Vq/1q/167d4/d8mTmQtg04AZQAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# 读取图像\n", "img1 = cv2.imread('t1.png', cv2.IMREAD_COLOR)\n", "img2 = cv2.imread('t2.png', cv2.IMREAD_COLOR)\n", "\n", "gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY) / 255.0\n", "gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY) / 255.0\n", "\n", "g_images1, corners1, scales1, orientations1, layers1 = detect_blobs(gray1)\n", "g_images2, corners2, scales2, orientations2, layers2 = detect_blobs(gray2)\n", "    \n", "descriptors1 = compute_descriptors(g_images1, corners1, scales1, orientations1, layers1)\n", "descriptors2 = compute_descriptors(g_images2, corners2, scales2, orientations2, layers2)\n", "    \n", "matches = match_descriptors(descriptors1, descriptors2)\n", "image1 = cv2.imread('t1.png', cv2.IMREAD_GRAYSCALE)\n", "image2 = cv2.imread('t2.png', cv2.IMREAD_GRAYSCALE)\n", "\n", "xform, outlier_labels = compute_affine_xform(corners1, corners2, matches)    \n", "match_image = draw_matches(image1, image2, corners1, corners2, matches,outlier_labels=outlier_labels)\n", "\n", "plt.title('Match')\n", "plt.imshow(match_image)\n", "plt.axis('off')\n", "plt.plot()    \n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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************************************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******************************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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["image1 = cv2.imread('t1.png')\n", "image2 = cv2.imread('t2.png')\n", "    \n", "image = stitch_images(image1, image2, xform)\n", "\n", "plt.title('Stich')\n", "plt.imshow(image.astype(np.uint8))\n", "plt.axis('off')\n", "plt.plot()    \n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8.5 总结"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在本章中，我们学习了利用如何进行图像拼接。我们首先利用SIFT算法提取出需要拼接的图像中的特征点，在此基础上使用RANSAC算法剔除无效点，之后进行匹配与图像变形融合，得到最终的拼接图像。由于篇幅的限制我们只做了两张图像的拼接，你也可以自己动手尝试多张图像的拼接。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 2}