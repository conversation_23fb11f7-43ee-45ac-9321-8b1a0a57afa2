import cv2
import numpy as np
import time

def detect_objects(image_path, template_path1, template_path2, template_path3, threshold=0.9):
    """
    使用两种不同模板检测图像中的目标
    
    参数:
    image_path: 原始图像路径
    template_path1: 模板1图像路径
    template_path2: 模板2图像路径
    threshold: 匹配阈值(0-1)，默认0.9
    
    返回:
    绘制检测框的彩色图像
    """
    # 读取原始图像
    img = image_path
    if img is None:
        raise FileNotFoundError(f"无法读取图像: {image_path}")
    img_gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 初始化矩形列表和置信度列表
    boxes = []
    confidences = []
    
    # 循环处理两个模板
    for template_path in [template_path1, template_path2, template_path3]:
        # 读取模板图像
        template = cv2.imread(template_path, cv2.IMREAD_GRAYSCALE)
        if template is None:
            print(f"警告: 无法读取模板 {template_path}, 跳过")
            continue
            
        h, w = template.shape[:2]
        
        # 模板匹配
        res = cv2.matchTemplate(img_gray, template, cv2.TM_CCOEFF_NORMED)
        
        # 找到所有匹配位置
        loc = np.where(res >= threshold)
        for pt in zip(*loc[::-1]):  # 交换x,y坐标
            # 将矩形信息添加到列表中 (x, y, w, h)
            boxes.append([pt[0], pt[1], w, h])
            # 记录匹配值作为置信度
            confidences.append(res[pt[1], pt[0]])
    
    # 非极大值抑制(NMS)处理重复检测
    boxes = np.array(boxes)
    confidences = np.array(confidences)
    indices = cv2.dnn.NMSBoxes(boxes, confidences, threshold, 0.4)
    
    # 绘制检测结果
    output_img = img.copy()
    for i in indices:
        x, y, w, h = boxes[i]
        cv2.rectangle(output_img, (x, y), (x + w, y + h), (0, 0, 255), 2)
    
    return output_img


pre_time = time.perf_counter_ns()

vc = cv2.VideoCapture(1)

# 检查打开是否正确
if vc.isOpened():
    open, frame = vc.read()
else:
    open = False
    print('False')


while open:
    ret, frame = vc.read()
    real_time = time.perf_counter_ns()
    if frame is None:
        break
    if ret is True:
        fps = 1000000000 / (real_time - pre_time)
        template_path1 = r"23_NUEDC_E\1.jpg"
        template_path2 = r"23_NUEDC_E\2.jpg"
        template_path3 = r"23_NUEDC_E\3.jpg"
        template_path4 = r"23_NUEDC_E\4.jpg"
        frame = detect_objects(frame, template_path1, template_path2, template_path3, threshold=0.8)
        cv2.putText(frame, str(fps), (0, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.65, (0, 255, 0), 1)
        cv2.imshow('result', frame)
        pre_time = time.perf_counter_ns()
        if cv2.waitKey(1) & 0xFF == 27:
            break
vc.release()
cv2.destroyAllWindows()
