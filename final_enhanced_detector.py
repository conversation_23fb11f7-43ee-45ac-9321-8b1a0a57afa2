#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终增强激光点检测器
结合高精度和高性能，减少误识别
专为ROI区域优化，适用于6Tops INT8设备
"""

import cv2
import numpy as np
import time

def detect_laser_point_final(image):
    """
    最终增强激光点检测
    
    参数:
        image: 输入图像 (BGR格式)
    
    返回:
        tuple: (x, y) 激光点坐标，如果未检测到则返回None
    """
    if image is None:
        return None
    
    candidates = []
    
    # 主要检测方法：增强亮度检测
    bright_candidates = _enhanced_brightness_detection(image)
    candidates.extend(bright_candidates)
    
    # 辅助检测方法：红色检测（仅在候选点不足时使用）
    if len(candidates) < 2:
        red_candidates = _enhanced_red_detection(image)
        candidates.extend(red_candidates)
    
    # 备用检测方法：圆形检测（仅在候选点很少时使用）
    if len(candidates) < 1:
        circle_candidates = _circle_detection(image)
        candidates.extend(circle_candidates)
    
    # 选择最佳候选点
    return _select_best_candidate(candidates)

def _enhanced_brightness_detection(image):
    """增强亮度检测 - 平衡版"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 计算图像统计信息
    mean_brightness = np.mean(gray)
    max_val = np.max(gray)
    
    # 亮度检查
    if max_val < 210 or (max_val - mean_brightness) < 25:
        return candidates
    
    # 多阈值检测
    thresholds = [max_val - 8, max_val - 15, max_val - 25]
    
    for threshold in thresholds:
        if threshold < 200:
            continue
            
        _, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)
        
        # 轻量级形态学操作
        kernel = np.ones((3, 3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 2 <= area <= 100:
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    
                    # 验证激光点特征
                    if _verify_laser_features(gray, (cx, cy)):
                        brightness_score = gray[cy, cx] / 255.0
                        area_score = min(area / 40.0, 1.0)
                        
                        # 计算圆形度
                        perimeter = cv2.arcLength(contour, True)
                        if perimeter > 0:
                            circularity = 4 * np.pi * area / (perimeter ** 2)
                            circularity_score = min(circularity, 1.0)
                        else:
                            circularity_score = 0
                        
                        # 综合得分
                        score = (brightness_score * 0.5 + 
                                area_score * 0.2 + 
                                circularity_score * 0.3)
                        
                        if score > 0.4:
                            candidates.append({
                                'point': (cx, cy),
                                'score': score,
                                'method': 'brightness'
                            })
    
    return candidates

def _verify_laser_features(gray, point):
    """验证激光点特征"""
    x, y = point
    h, w = gray.shape
    
    # 边界检查
    if x < 4 or y < 4 or x >= w-4 or y >= h-4:
        return False
    
    # 中心亮度检查
    center_val = gray[y, x]
    if center_val < 200:
        return False
    
    # 局部对比度检查
    local_region = gray[y-3:y+4, x-3:x+4]
    avg_surrounding = np.mean(local_region)
    contrast = center_val - avg_surrounding
    
    if contrast < 15:
        return False
    
    # 检查中心是否为局部最大值
    center_3x3 = gray[y-1:y+2, x-1:x+2]
    if center_val < np.max(center_3x3):
        return False
    
    return True

def _enhanced_red_detection(image):
    """增强红色检测"""
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 红色范围检测
    lower_red1 = np.array([0, 80, 120])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([170, 80, 120])
    upper_red2 = np.array([180, 255, 255])
    
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)
    
    # 亮度掩码
    _, brightness_mask = cv2.threshold(gray, 180, 255, cv2.THRESH_BINARY)
    
    # 融合掩码
    final_mask = cv2.bitwise_and(red_mask, brightness_mask)
    
    # 形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel)
    
    # 查找轮廓
    contours, _ = cv2.findContours(final_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if 2 <= area <= 120:
            M = cv2.moments(contour)
            if M["m00"] != 0:
                cx = int(M["m10"] / M["m00"])
                cy = int(M["m01"] / M["m00"])
                
                # 验证红色特征
                if _verify_red_features(image, (cx, cy)):
                    score = min(area / 60.0, 1.0)
                    candidates.append({
                        'point': (cx, cy),
                        'score': score,
                        'method': 'red'
                    })
    
    return candidates

def _verify_red_features(image, point):
    """验证红色特征"""
    x, y = point
    h, w = image.shape[:2]
    
    if x < 2 or y < 2 or x >= w-2 or y >= h-2:
        return False
    
    # 提取BGR值
    b, g, r = image[y, x]
    
    # 红色分量应该明显高于其他分量
    if r < 120 or r < g + 20 or r < b + 20:
        return False
    
    return True

def _circle_detection(image):
    """圆形检测"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    candidates = []
    
    # 高斯模糊
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    
    # 霍夫圆检测
    circles = cv2.HoughCircles(
        blurred, cv2.HOUGH_GRADIENT,
        dp=1, minDist=10,
        param1=60, param2=20,
        minRadius=1, maxRadius=8
    )
    
    if circles is not None:
        circles = np.round(circles[0, :]).astype("int")
        for (x, y, r) in circles[:3]:
            if 0 <= x < gray.shape[1] and 0 <= y < gray.shape[0]:
                center_val = gray[y, x]
                if center_val > 200:
                    score = center_val / 255.0
                    candidates.append({
                        'point': (x, y),
                        'score': score,
                        'method': 'circle'
                    })
    
    return candidates

def _select_best_candidate(candidates):
    """选择最佳候选点"""
    if not candidates:
        return None
    
    if len(candidates) == 1:
        return candidates[0]['point']
    
    # 按得分排序
    candidates = sorted(candidates, key=lambda x: x['score'], reverse=True)
    
    # 简单聚类
    best_candidate = None
    best_score = 0
    
    for candidate in candidates:
        point = candidate['point']
        score = candidate['score']
        
        # 检查周围是否有其他高分候选点
        nearby_scores = []
        for other in candidates:
            if other == candidate:
                continue
            other_point = other['point']
            distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
            if distance <= 10:
                nearby_scores.append(other['score'])
        
        # 如果有相近的高分候选点，给予奖励
        if nearby_scores:
            avg_nearby = np.mean(nearby_scores)
            cluster_bonus = avg_nearby * 0.3
        else:
            cluster_bonus = 0
        
        total_score = score + cluster_bonus
        
        if total_score > best_score:
            best_score = total_score
            best_candidate = candidate
    
    return best_candidate['point'] if best_candidate else None

class LaserTracker:
    """激光点跟踪器"""
    
    def __init__(self, history_size=3):
        self.history = []
        self.history_size = history_size
        
    def update(self, detection):
        """更新检测结果"""
        if detection is not None:
            self.history.append(detection)
            if len(self.history) > self.history_size:
                self.history.pop(0)
            
            # 返回平均位置
            if len(self.history) >= 2:
                avg_x = sum(p[0] for p in self.history) // len(self.history)
                avg_y = sum(p[1] for p in self.history) // len(self.history)
                return (avg_x, avg_y)
            else:
                return detection
        else:
            # 逐渐减少历史记录
            if self.history:
                self.history.pop(0)
            return self.history[-1] if self.history else None

def test_final_detector():
    """测试最终检测器"""
    cap = cv2.VideoCapture('output.mp4')
    
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    tracker = LaserTracker()
    frame_count = 0
    detection_count = 0
    total_time = 0
    
    print("开始最终增强激光点检测测试...")
    print("目标: 减少误识别，保持高检测率")
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # 调整到目标分辨率
        frame = cv2.resize(frame, (640, 480))
        
        # 保存原始帧用于显示
        display_frame = frame.copy()
        
        # 提取ROI区域
        roi_frame = frame[65:410, 260:470, :]
        roi_offset = (260, 65)
        
        start_time = time.time()
        
        # 最终检测
        laser_point = detect_laser_point_final(roi_frame)
        
        # 使用跟踪器稳定结果
        if laser_point:
            original_point = (laser_point[0] + roi_offset[0], laser_point[1] + roi_offset[1])
            tracked_point = tracker.update(original_point)
        else:
            tracked_point = tracker.update(None)
        
        end_time = time.time()
        total_time += (end_time - start_time)
        
        if tracked_point:
            detection_count += 1
            x, y = tracked_point
            
            if frame_count <= 20 or frame_count % 50 == 0:
                roi_x = laser_point[0] if laser_point else "N/A"
                roi_y = laser_point[1] if laser_point else "N/A"
                print(f"Frame {frame_count}: 激光点 ({x}, {y}) [ROI: ({roi_x}, {roi_y})]")
            
            # 绘制检测结果
            cv2.circle(display_frame, (x, y), 8, (0, 255, 0), 2)
            cv2.circle(display_frame, (x, y), 3, (0, 0, 255), -1)
            cv2.putText(display_frame, f"({x}, {y})", (x+15, y-15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 绘制ROI区域
            cv2.rectangle(display_frame, (260, 65), (470, 410), (255, 255, 0), 2)
            cv2.putText(display_frame, "ROI", (265, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        # 显示信息
        if frame_count > 1:
            current_fps = frame_count / total_time
            cv2.putText(display_frame, f"FPS: {current_fps:.1f}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            detection_rate = detection_count / frame_count * 100
            cv2.putText(display_frame, f"Detection: {detection_rate:.1f}%", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 显示结果
        if frame_count % 3 == 0:
            cv2.imshow('Final Enhanced Laser Detection', display_frame)
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s') and tracked_point:
                filename = f"final_enhanced_{frame_count:04d}.jpg"
                cv2.imwrite(filename, display_frame)
                print(f"保存图像: {filename}")
    
    cap.release()
    cv2.destroyAllWindows()
    
    # 统计结果
    avg_fps = frame_count / total_time if total_time > 0 else 0
    detection_rate = detection_count / frame_count * 100
    avg_time_per_frame = total_time / frame_count * 1000 if frame_count > 0 else 0
    
    print(f"\n=== 最终检测结果统计 ===")
    print(f"总帧数: {frame_count}")
    print(f"检测到激光点的帧数: {detection_count}")
    print(f"检测率: {detection_rate:.1f}%")
    print(f"平均FPS: {avg_fps:.1f}")
    print(f"平均处理时间: {avg_time_per_frame:.2f}ms/帧")
    print(f"目标性能: 20 FPS @ 640x480")
    print(f"性能达标: {'✓' if avg_fps >= 20 else '✗'}")

if __name__ == "__main__":
    print("最终增强激光点检测器")
    print("平衡精度与性能，减少误识别")
    test_final_detector()
